<?php
require '../koneksi.php';

// Get statistics for dashboard
$total_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek"))['count'];
$pending_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'pending'"))['count'];
$in_progress_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'proses'"))['count'];
$completed_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'selesai'"))['count'];
$verification_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE status = 'verifikasi'"))['count'];

// Get verification statistics
$total_verifications = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM verifikasi"))['count'];
$approved_count = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM verifikasi WHERE status_verifikasi = 'approved'"))['count'];
$pending_verifications = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM verifikasi WHERE status_verifikasi = 'pending'"))['count'];

// Get revision request statistics
$revision_stats_query = "SELECT
                            COUNT(*) as total_revisions,
                            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_revisions,
                            SUM(CASE WHEN status = 'in_review' THEN 1 ELSE 0 END) as in_review_revisions,
                            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_revisions,
                            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_revisions,
                            SUM(CASE WHEN priority_level = 'urgent' THEN 1 ELSE 0 END) as urgent_revisions
                        FROM revision_requests";
$revision_stats_result = mysqli_query($koneksi, $revision_stats_query);
$revision_stats = mysqli_fetch_array($revision_stats_result);

// Get recent revision requests that need attention
$recent_revisions_query = "SELECT rr.*, p.nama_petugas as client_name
                           FROM revision_requests rr
                           LEFT JOIN petugas p ON rr.client_id = p.id_petugas
                           WHERE rr.status IN ('pending', 'in_review', 'in_progress')
                           ORDER BY
                               CASE rr.priority_level
                                   WHEN 'urgent' THEN 1
                                   WHEN 'high' THEN 2
                                   WHEN 'medium' THEN 3
                                   WHEN 'low' THEN 4
                               END,
                               rr.created_at DESC
                           LIMIT 5";
$recent_revisions_result = mysqli_query($koneksi, $recent_revisions_query);
?>

<!-- Welcome Header -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">
        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard Tim Proyek Antosa Arsitek
    </h1>
    <div class="text-right">
        <small class="text-muted">Selamat datang, <strong><?php echo $_SESSION['nama']; ?></strong></small>
    </div>
</div>

<!-- Statistics Cards Row -->
<div class="row">
    <!-- Total Tasks Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Tugas</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_tasks; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Tasks Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $pending_tasks; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- In Progress Tasks Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Dalam Proses</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $in_progress_tasks; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-spinner fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Completed Tasks Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Selesai</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $completed_tasks; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revision Request Statistics -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Revisi</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $revision_stats['total_revisions']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-edit fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending Revisi</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $revision_stats['pending_revisions']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Mendesak</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $revision_stats['urgent_revisions']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Selesai</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $revision_stats['completed_revisions']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Row -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt mr-2"></i>Aksi Cepat
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-primary btn-block" data-toggle="modal" data-target="#inputTugasModal">
                            <i class="fas fa-plus mr-2"></i>Input Tugas Baru
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-info btn-block" data-toggle="modal" data-target="#uploadFileModal">
                            <i class="fas fa-upload mr-2"></i>Upload File Desain
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="tugas_harian.php" class="btn btn-success btn-block">
                            <i class="fas fa-list mr-2"></i>Lihat Semua Tugas
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="kelola_revisi.php" class="btn btn-secondary btn-block">
                            <i class="fas fa-edit mr-2"></i>Kelola Revisi
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="verifikasi.php" class="btn btn-warning btn-block">
                            <i class="fas fa-check-double mr-2"></i>Verifikasi
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Revision Requests Section -->
<div class="row mb-4">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-edit mr-2"></i>Permintaan Revisi yang Perlu Perhatian
                </h6>
                <a href="kelola_revisi.php" class="btn btn-primary btn-sm">
                    <i class="fas fa-list mr-1"></i>Lihat Semua
                </a>
            </div>
            <div class="card-body">
                <?php if (mysqli_num_rows($recent_revisions_result) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Judul</th>
                                    <th>Client</th>
                                    <th>Prioritas</th>
                                    <th>Status</th>
                                    <th>Tanggal</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($revision = mysqli_fetch_array($recent_revisions_result)): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($revision['title']); ?></td>
                                        <td><?php echo htmlspecialchars($revision['client_name']); ?></td>
                                        <td>
                                            <span class="badge badge-<?php
                                                switch($revision['priority_level']) {
                                                    case 'urgent': echo 'danger'; break;
                                                    case 'high': echo 'warning'; break;
                                                    case 'medium': echo 'info'; break;
                                                    case 'low': echo 'secondary'; break;
                                                    default: echo 'secondary';
                                                }
                                            ?>">
                                                <?php
                                                $priority_labels = [
                                                    'urgent' => 'Mendesak',
                                                    'high' => 'Tinggi',
                                                    'medium' => 'Sedang',
                                                    'low' => 'Rendah'
                                                ];
                                                echo $priority_labels[$revision['priority_level']] ?? $revision['priority_level'];
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php
                                                switch($revision['status']) {
                                                    case 'pending': echo 'warning'; break;
                                                    case 'in_review': echo 'info'; break;
                                                    case 'in_progress': echo 'primary'; break;
                                                    case 'completed': echo 'success'; break;
                                                    case 'rejected': echo 'danger'; break;
                                                    default: echo 'secondary';
                                                }
                                            ?>">
                                                <?php
                                                $status_labels = [
                                                    'pending' => 'Pending',
                                                    'in_review' => 'Review',
                                                    'in_progress' => 'Progress',
                                                    'completed' => 'Selesai',
                                                    'rejected' => 'Ditolak'
                                                ];
                                                echo $status_labels[$revision['status']] ?? $revision['status'];
                                                ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('d/m/Y', strtotime($revision['created_at'])); ?></td>
                                        <td>
                                            <a href="kelola_revisi.php?id=<?php echo $revision['id']; ?>" class="btn btn-info btn-sm">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3"></i>
                        <h5>Tidak ada permintaan revisi yang perlu perhatian</h5>
                        <p>Semua permintaan revisi telah diproses atau belum ada permintaan baru.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Recent Tasks and Verification Status Row -->
<div class="row">
    <!-- Recent Tasks -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-clock mr-2"></i>Tugas Terbaru
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Nama Tugas</th>
                                <th>Tanggal</th>
                                <th>Status</th>
                                <th>Progress</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $recent_tasks = mysqli_query($koneksi, "SELECT * FROM tugas_proyek ORDER BY tgl DESC LIMIT 5");
                            while ($task = mysqli_fetch_array($recent_tasks)) {
                                $status = $task['status'];
                                $badgeClass = 'secondary';
                                $statusText = ucfirst($status);
                                $progress = 0;

                                if ($status == 'pending') {
                                    $badgeClass = 'secondary';
                                    $statusText = 'Pending';
                                    $progress = 0;
                                } else if ($status == 'proses') {
                                    $badgeClass = 'warning';
                                    $statusText = 'Dalam Proses';
                                    $progress = 50;
                                } else if ($status == 'verifikasi') {
                                    $badgeClass = 'info';
                                    $statusText = 'Siap Verifikasi';
                                    $progress = 90;
                                } else if ($status == 'selesai') {
                                    $badgeClass = 'success';
                                    $statusText = 'Selesai';
                                    $progress = 100;
                                } else if ($status == 'batal') {
                                    $badgeClass = 'danger';
                                    $statusText = 'Batal';
                                    $progress = 0;
                                }

                                // Use progress_percentage from database if it exists, otherwise use calculated progress
                                if (isset($task['progress_percentage']) && $task['progress_percentage'] !== null) {
                                    $progress = $task['progress_percentage'];
                                }
                                ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($task['nama_kegiatan']); ?></strong></td>
                                    <td><?php echo date('d/m/Y', strtotime($task['tgl'])); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo $badgeClass; ?>">
                                            <?php echo $statusText; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 15px;">
                                            <div class="progress-bar bg-<?php echo $badgeClass; ?>" role="progressbar"
                                                 style="width: <?php echo $progress; ?>%"
                                                 aria-valuenow="<?php echo $progress; ?>"
                                                 aria-valuemin="0" aria-valuemax="100">
                                                <?php echo $progress; ?>%
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="tugas_harian.php" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye mr-1"></i>Lihat Semua Tugas
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Verification Summary -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-check-double mr-2"></i>Status Verifikasi
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm">Total Verifikasi</span>
                        <span class="font-weight-bold"><?php echo $total_verifications; ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm text-success">Disetujui</span>
                        <span class="font-weight-bold text-success"><?php echo $approved_count; ?></span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="text-sm text-warning">Pending</span>
                        <span class="font-weight-bold text-warning"><?php echo $pending_verifications; ?></span>
                    </div>
                </div>
                <div class="text-center">
                    <a href="verifikasi.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-external-link-alt mr-1"></i>Detail Verifikasi
                    </a>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card shadow mt-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-chart-pie mr-2"></i>Ringkasan Hari Ini
                </h6>
            </div>
            <div class="card-body">
                <?php
                $today = date('Y-m-d');
                $today_tasks = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE DATE(tgl) = '$today'"))['count'];
                $today_completed = mysqli_fetch_array(mysqli_query($koneksi, "SELECT COUNT(*) as count FROM tugas_proyek WHERE DATE(tgl) = '$today' AND status = 'selesai'"))['count'];
                ?>
                <div class="text-center">
                    <div class="mb-2">
                        <span class="h4 font-weight-bold text-primary"><?php echo $today_tasks; ?></span>
                        <div class="text-sm text-muted">Tugas Hari Ini</div>
                    </div>
                    <div class="mb-2">
                        <span class="h4 font-weight-bold text-success"><?php echo $today_completed; ?></span>
                        <div class="text-sm text-muted">Selesai Hari Ini</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal Input Tugas -->
<div class="modal fade" id="inputTugasModal" tabindex="-1" role="dialog" aria-labelledby="inputTugasModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="inputTugasModalLabel">
                    <i class="fas fa-plus mr-2"></i>Input Tugas Baru
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="simpan_input.php" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="nama_kegiatan">Nama Kegiatan</label>
                        <input type="text" class="form-control" id="nama_kegiatan" name="nama_kegiatan"
                            placeholder="Contoh: Pengecoran Lantai 2" required>
                    </div>

                    <div class="form-group">
                        <label for="deskripsi">Deskripsi Pekerjaan</label>
                        <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"
                            placeholder="Deskripsikan progres pekerjaan di lapangan" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="tgl">Tanggal Pengerjaan</label>
                        <input type="date" class="form-control" id="tgl" name="tgl" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i>Simpan Tugas
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Upload File -->
<div class="modal fade" id="uploadFileModal" tabindex="-1" role="dialog" aria-labelledby="uploadFileModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadFileModalLabel">
                    <i class="fas fa-upload mr-2"></i>Upload File Desain
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form action="simpan_file.php" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="deskripsi_file">Deskripsi File</label>
                        <input type="text" name="deskripsi" class="form-control"
                            placeholder="Masukkan deskripsi..." maxlength="200" required>
                        <small class="form-text text-muted">Maksimal 200 karakter</small>
                    </div>

                    <div class="form-group">
                        <label for="file_upload">Pilih File Desain</label>
                        <input type="file" name="gambar" class="form-control-file" required>
                        <small class="form-text text-muted">
                            File yang diperbolehkan: .jpg, .png, .pdf, .obj, .stl, .dwg (Maksimal 50MB)
                        </small>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>Tips:</strong> Pastikan file desain sudah dalam format yang benar dan ukuran tidak melebihi 50MB.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload mr-1"></i>Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>