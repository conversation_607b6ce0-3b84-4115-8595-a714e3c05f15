<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Get form data
    $client_id = $_SESSION['id_petugas'];
    $title = mysqli_real_escape_string($koneksi, $_POST['title']);
    $revision_type = mysqli_real_escape_string($koneksi, $_POST['revision_type']);
    $priority_level = mysqli_real_escape_string($koneksi, $_POST['priority_level']);
    $description = mysqli_real_escape_string($koneksi, $_POST['description']);
    $client_notes = mysqli_real_escape_string($koneksi, $_POST['client_notes']);
    $tugas_id = !empty($_POST['tugas_id']) ? intval($_POST['tugas_id']) : null;
    $file_id = !empty($_POST['file_id']) ? intval($_POST['file_id']) : null;

    // Validate required fields
    if (empty($title) || empty($revision_type) || empty($priority_level) || empty($description)) {
        echo "<script>alert('Semua field yang wajib harus diisi!'); window.history.back();</script>";
        exit;
    }

    // Validate enum values
    $valid_types = ['design', 'document', 'specification', 'other'];
    $valid_priorities = ['low', 'medium', 'high', 'urgent'];
    
    if (!in_array($revision_type, $valid_types)) {
        echo "<script>alert('Jenis revisi tidak valid!'); window.history.back();</script>";
        exit;
    }
    
    if (!in_array($priority_level, $valid_priorities)) {
        echo "<script>alert('Tingkat prioritas tidak valid!'); window.history.back();</script>";
        exit;
    }

    // Start transaction
    mysqli_begin_transaction($koneksi);

    try {
        // Insert revision request
        $query = "INSERT INTO revision_requests 
                  (client_id, tugas_id, file_id, revision_type, priority_level, title, description, client_notes, status, created_at) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";
        
        $stmt = mysqli_prepare($koneksi, $query);
        mysqli_stmt_bind_param($stmt, "iiisssss", 
            $client_id, $tugas_id, $file_id, $revision_type, $priority_level, $title, $description, $client_notes);
        
        if (!mysqli_stmt_execute($stmt)) {
            throw new Exception('Gagal menyimpan pengajuan revisi: ' . mysqli_error($koneksi));
        }

        $revision_id = mysqli_insert_id($koneksi);

        // Commit transaction
        mysqli_commit($koneksi);

        // Success message and redirect
        echo "<script>
                alert('Pengajuan revisi berhasil dikirim! Tim proyek akan segera meninjau permintaan Anda.');
                window.location.href = 'client.php?success=revision_submitted';
              </script>";

    } catch (Exception $e) {
        // Rollback transaction
        mysqli_rollback($koneksi);
        
        echo "<script>
                alert('Terjadi kesalahan: " . $e->getMessage() . "');
                window.history.back();
              </script>";
    }

    mysqli_stmt_close($stmt);
    mysqli_close($koneksi);

} else {
    // If not POST request, redirect back
    header("Location: ajukan_revisi.php");
    exit;
}
?>
