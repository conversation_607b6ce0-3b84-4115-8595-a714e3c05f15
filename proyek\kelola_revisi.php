<?php
session_start();
require '../koneksi.php';

// Check if user is logged in and has project team access
if (!isset($_SESSION['id_petugas']) || $_SESSION['level'] != 'proyek') {
    header("Location: ../login.php");
    exit();
}

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$priority_filter = isset($_GET['priority']) ? $_GET['priority'] : '';
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Build query with filters
$where_conditions = [];
$params = [];

if (!empty($status_filter)) {
    $where_conditions[] = "rr.status = ?";
    $params[] = $status_filter;
}

if (!empty($priority_filter)) {
    $where_conditions[] = "rr.priority_level = ?";
    $params[] = $priority_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(rr.title LIKE ? OR rr.description LIKE ? OR p.nama_petugas LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get revision requests with client information
$query = "SELECT rr.*, p.nama_petugas as client_name, tp.nama_kegiatan, fg.deskripsi as file_description
          FROM revision_requests rr
          LEFT JOIN petugas p ON rr.client_id = p.id_petugas
          LEFT JOIN tugas_proyek tp ON rr.tugas_id = tp.id
          LEFT JOIN file_gambar fg ON rr.file_id = fg.id
          $where_clause
          ORDER BY
              CASE rr.priority_level
                  WHEN 'urgent' THEN 1
                  WHEN 'high' THEN 2
                  WHEN 'medium' THEN 3
                  WHEN 'low' THEN 4
              END,
              rr.created_at DESC";

$stmt = mysqli_prepare($koneksi, $query);
if (!empty($params)) {
    $types = str_repeat('s', count($params));
    mysqli_stmt_bind_param($stmt, $types, ...$params);
}
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

// Get statistics
$stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'in_review' THEN 1 ELSE 0 END) as in_review,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN priority_level = 'urgent' THEN 1 ELSE 0 END) as urgent
                FROM revision_requests";
$stats_result = mysqli_query($koneksi, $stats_query);
$stats = mysqli_fetch_array($stats_result);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Kelola Revisi - Antosa Arsitek</title>
    <link href="../vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="../vendor/datatables/dataTables.bootstrap4.min.css" rel="stylesheet">
    <link href="../css/sb-admin-2.min.css" rel="stylesheet">
</head>

<body id="page-top">
    <div id="wrapper">
        <?php include 'proyek.php'; ?>
        
        <div id="content-wrapper" class="d-flex flex-column">
            <div id="content">
                <div class="container-fluid">
                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-edit mr-2"></i>Kelola Permintaan Revisi
                        </h1>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-xl-2 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-edit fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['pending']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Review</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['in_review']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-search fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Progress</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['in_progress']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-tasks fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Selesai</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['completed']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-check fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-6 mb-4">
                            <div class="card border-left-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Mendesak</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['urgent']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Filter & Pencarian</h6>
                        </div>
                        <div class="card-body">
                            <form method="GET" class="row">
                                <div class="col-md-3 mb-3">
                                    <label for="status">Status:</label>
                                    <select name="status" id="status" class="form-control">
                                        <option value="">Semua Status</option>
                                        <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="in_review" <?php echo $status_filter == 'in_review' ? 'selected' : ''; ?>>Review</option>
                                        <option value="in_progress" <?php echo $status_filter == 'in_progress' ? 'selected' : ''; ?>>Progress</option>
                                        <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>Selesai</option>
                                        <option value="rejected" <?php echo $status_filter == 'rejected' ? 'selected' : ''; ?>>Ditolak</option>
                                    </select>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <label for="priority">Prioritas:</label>
                                    <select name="priority" id="priority" class="form-control">
                                        <option value="">Semua Prioritas</option>
                                        <option value="urgent" <?php echo $priority_filter == 'urgent' ? 'selected' : ''; ?>>Mendesak</option>
                                        <option value="high" <?php echo $priority_filter == 'high' ? 'selected' : ''; ?>>Tinggi</option>
                                        <option value="medium" <?php echo $priority_filter == 'medium' ? 'selected' : ''; ?>>Sedang</option>
                                        <option value="low" <?php echo $priority_filter == 'low' ? 'selected' : ''; ?>>Rendah</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="search">Pencarian:</label>
                                    <input type="text" name="search" id="search" class="form-control" placeholder="Cari judul, deskripsi, atau nama client..." value="<?php echo htmlspecialchars($search); ?>">
                                </div>
                                <div class="col-md-2 mb-3">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary btn-block">
                                            <i class="fas fa-search mr-1"></i>Filter
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Revision Requests Table -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Daftar Permintaan Revisi</h6>
                        </div>
                        <div class="card-body">
                            <?php if (mysqli_num_rows($result) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>Judul</th>
                                                <th>Client</th>
                                                <th>Prioritas</th>
                                                <th>Status</th>
                                                <th>Tanggal</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($row = mysqli_fetch_array($result)): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($row['title']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars(substr($row['description'], 0, 100)) . (strlen($row['description']) > 100 ? '...' : ''); ?></small>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($row['client_name']); ?></td>
                                                    <td>
                                                        <span class="badge badge-<?php
                                                            switch($row['priority_level']) {
                                                                case 'urgent': echo 'danger'; break;
                                                                case 'high': echo 'warning'; break;
                                                                case 'medium': echo 'info'; break;
                                                                case 'low': echo 'secondary'; break;
                                                                default: echo 'secondary';
                                                            }
                                                        ?>">
                                                            <?php
                                                            $priority_labels = [
                                                                'urgent' => 'Mendesak',
                                                                'high' => 'Tinggi',
                                                                'medium' => 'Sedang',
                                                                'low' => 'Rendah'
                                                            ];
                                                            echo $priority_labels[$row['priority_level']] ?? $row['priority_level'];
                                                            ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-<?php
                                                            switch($row['status']) {
                                                                case 'pending': echo 'warning'; break;
                                                                case 'in_review': echo 'info'; break;
                                                                case 'in_progress': echo 'primary'; break;
                                                                case 'completed': echo 'success'; break;
                                                                case 'rejected': echo 'danger'; break;
                                                                default: echo 'secondary';
                                                            }
                                                        ?>">
                                                            <?php
                                                            $status_labels = [
                                                                'pending' => 'Pending',
                                                                'in_review' => 'Review',
                                                                'in_progress' => 'Progress',
                                                                'completed' => 'Selesai',
                                                                'rejected' => 'Ditolak'
                                                            ];
                                                            echo $status_labels[$row['status']] ?? $row['status'];
                                                            ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-info btn-sm" onclick="viewRevision(<?php echo $row['id']; ?>)">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <?php if ($row['status'] != 'completed' && $row['status'] != 'rejected'): ?>
                                                                <button type="button" class="btn btn-primary btn-sm" onclick="updateStatus(<?php echo $row['id']; ?>, '<?php echo $row['status']; ?>')">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                                    <h5 class="text-gray-600">Tidak ada permintaan revisi</h5>
                                    <p class="text-muted">Belum ada permintaan revisi yang sesuai dengan filter yang dipilih.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- View Revision Modal -->
    <div class="modal fade" id="viewRevisionModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Detail Permintaan Revisi</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="revisionDetails">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Update Status Revisi</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <form id="updateStatusForm">
                    <div class="modal-body">
                        <input type="hidden" id="revisionId" name="revision_id">
                        <div class="form-group">
                            <label for="newStatus">Status Baru:</label>
                            <select class="form-control" id="newStatus" name="new_status" required>
                                <option value="">Pilih Status</option>
                                <option value="in_review">Review</option>
                                <option value="in_progress">Progress</option>
                                <option value="completed">Selesai</option>
                                <option value="rejected">Ditolak</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="responseMessage">Pesan Respon:</label>
                            <textarea class="form-control" id="responseMessage" name="response_message" rows="3" placeholder="Berikan keterangan atau feedback..."></textarea>
                        </div>
                        <div class="form-group" id="completionDateGroup" style="display: none;">
                            <label for="estimatedCompletion">Estimasi Selesai:</label>
                            <input type="date" class="form-control" id="estimatedCompletion" name="estimated_completion">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
                        <button type="submit" class="btn btn-primary">Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../vendor/jquery/jquery.min.js"></script>
    <script src="../vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../vendor/datatables/jquery.dataTables.min.js"></script>
    <script src="../vendor/datatables/dataTables.bootstrap4.min.js"></script>
    <script src="../js/sb-admin-2.min.js"></script>

    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
                },
                "order": [[ 4, "desc" ]]
            });

            // Show/hide completion date based on status
            $('#newStatus').change(function() {
                if ($(this).val() === 'in_progress') {
                    $('#completionDateGroup').show();
                } else {
                    $('#completionDateGroup').hide();
                }
            });

            // Handle status update form submission
            $('#updateStatusForm').submit(function(e) {
                e.preventDefault();

                $.ajax({
                    url: 'proses_kelola_revisi.php',
                    type: 'POST',
                    data: $(this).serialize() + '&action=update_status',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            alert('Status berhasil diupdate!');
                            location.reload();
                        } else {
                            alert('Error: ' + response.message);
                        }
                    },
                    error: function() {
                        alert('Terjadi kesalahan saat mengupdate status.');
                    }
                });
            });
        });

        function viewRevision(id) {
            $.ajax({
                url: 'proses_kelola_revisi.php',
                type: 'POST',
                data: { action: 'get_revision_details', revision_id: id },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        $('#revisionDetails').html(response.html);
                        $('#viewRevisionModal').modal('show');
                    } else {
                        alert('Error: ' + response.message);
                    }
                },
                error: function() {
                    alert('Terjadi kesalahan saat memuat detail revisi.');
                }
            });
        }

        function updateStatus(id, currentStatus) {
            $('#revisionId').val(id);
            $('#newStatus').val('');
            $('#responseMessage').val('');
            $('#estimatedCompletion').val('');
            $('#completionDateGroup').hide();

            // Remove options that don't make sense based on current status
            $('#newStatus option').show();
            if (currentStatus === 'pending') {
                $('#newStatus option[value="pending"]').hide();
            } else if (currentStatus === 'in_review') {
                $('#newStatus option[value="pending"], #newStatus option[value="in_review"]').hide();
            } else if (currentStatus === 'in_progress') {
                $('#newStatus option[value="pending"], #newStatus option[value="in_review"], #newStatus option[value="in_progress"]').hide();
            }

            $('#updateStatusModal').modal('show');
        }
    </script>

</body>
</html>
