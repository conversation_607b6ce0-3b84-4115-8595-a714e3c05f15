<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

// Get user accessible files - simplified based on role only
$user_id = $_SESSION['id_petugas'] ?? 0;
$user_level = $_SESSION['level'];

// Simple role-based access control
if ($user_level == 'admin' || $user_level == 'proyek') {
    // Admin and project team can access all files
    $query = "SELECT fg.*, p.nama_petugas as uploader_name
              FROM file_gambar fg
              LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
              ORDER BY fg.created_at DESC";
    $result = mysqli_query($koneksi, $query);
} else {
    // Client can access all files for review purposes
    $query = "SELECT fg.*, p.nama_petugas as uploader_name
              FROM file_gambar fg
              LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
              ORDER BY fg.created_at DESC";
    $result = mysqli_query($koneksi, $query);
}

// Convert result to array for compatibility
$files = [];
if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $files[] = $row;
    }
}

// Filter parameters
$type_filter = $_GET['type'] ?? '';
$date_filter = $_GET['date'] ?? '';

// Apply filters
if (!empty($type_filter) || !empty($date_filter)) {
    $files = array_filter($files, function($file) use ($type_filter, $date_filter) {
        $match = true;

        if (!empty($type_filter)) {
            $file_ext = strtolower(pathinfo($file['gambar'], PATHINFO_EXTENSION));
            if ($file_ext != $type_filter) {
                $match = false;
            }
        }

        if (!empty($date_filter)) {
            $file_date = date('Y-m-d', strtotime($file['created_at']));
            if ($file_date != $date_filter) {
                $match = false;
            }
        }

        return $match;
    });
}

// Helper function to format file size
function formatFileSize($bytes) {
    if (!$bytes) return 'Unknown';
    $sizes = ['Bytes', 'KB', 'MB', 'GB'];
    $i = floor(log($bytes) / log(1024));
    return round($bytes / pow(1024, $i) * 100) / 100 . ' ' . $sizes[$i];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>File Management - Client</title>
    
    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">
    
    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">
    
    <style>
        .file-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .file-item:hover {
            background-color: #f8f9fc;
            border-left-color: #4e73df;
            transform: translateX(5px);
        }

        .file-icon {
            font-size: 2rem;
            width: 50px;
            text-align: center;
        }

        .file-type-pdf { color: #dc3545; }
        .file-type-dwg { color: #28a745; }
        .file-type-jpg, .file-type-png, .file-type-gif { color: #ffc107; }
        .file-type-default { color: #6c757d; }

        .filter-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            color: white;
        }

        .filter-section .form-control {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }



        .file-list-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .file-header {
            background: linear-gradient(135deg, #4e73df, #224abe);
            color: white;
            padding: 1.5rem;
            margin: 0;
        }

        .btn-action {
            border-radius: 20px;
            padding: 0.375rem 1rem;
            font-size: 0.875rem;
            margin: 0 0.25rem;
        }

        .empty-state {
            padding: 4rem 2rem;
            text-align: center;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
    </style>
</head>

<body id="page-top">
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="client.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">Client Portal</div>
            </a>

            <hr class="sidebar-divider my-0">

            <li class="nav-item">
                <a class="nav-link" href="client.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </li>

            <hr class="sidebar-divider">

            <div class="sidebar-heading">Interface</div>

            <li class="nav-item">
                <a class="nav-link" href="lihat_progress.php">
                    <i class="fas fa-fw fa-chart-line"></i>
                    <span>Lihat Progress</span>
                </a>
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="file_management.php">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>File Management</span>
                </a>
            </li>

            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span>
                </a>
            </li>

            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>
        </ul>

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <div id="content">
                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>
                    <h1 class="h4 mb-0 text-gray-800">File Management</h1>
                </nav>

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Filters -->
                    <div class="filter-section">
                        <h4 class="mb-3"><i class="fas fa-filter mr-2"></i>Filter Files</h4>
                        <form method="GET" class="row">
                            <div class="col-md-4 mb-3">
                                <label for="type" class="form-label">Jenis File:</label>
                                <select name="type" id="type" class="form-control">
                                    <option value="">Semua Jenis</option>
                                    <option value="pdf" <?= $type_filter == 'pdf' ? 'selected' : '' ?>>PDF Documents</option>
                                    <option value="jpg" <?= $type_filter == 'jpg' ? 'selected' : '' ?>>JPG Images</option>
                                    <option value="png" <?= $type_filter == 'png' ? 'selected' : '' ?>>PNG Images</option>
                                    <option value="dwg" <?= $type_filter == 'dwg' ? 'selected' : '' ?>>CAD Files (DWG)</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="date" class="form-label">Tanggal Upload:</label>
                                <input type="date" name="date" id="date" class="form-control" value="<?= htmlspecialchars($date_filter) ?>">
                            </div>
                            <div class="col-md-4 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-light btn-action mr-2">
                                    <i class="fas fa-search mr-1"></i>Filter
                                </button>
                                <a href="file_management.php" class="btn btn-outline-light btn-action">
                                    <i class="fas fa-undo mr-1"></i>Reset
                                </a>
                            </div>
                        </form>
                    </div>

                    <!-- Files List -->
                    <div class="file-list-container">
                        <div class="file-header">
                            <h4 class="mb-0"><i class="fas fa-folder-open mr-2"></i>File Management</h4>
                            <p class="mb-0 mt-1 opacity-75">Kelola dan akses file proyek Anda</p>
                        </div>

                        <?php if (empty($files)): ?>
                            <div class="empty-state">
                                <i class="fas fa-folder-open"></i>
                                <h5>Tidak ada file yang tersedia</h5>
                                <p>Belum ada file yang dapat diakses atau sesuai dengan filter yang dipilih.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="bg-light">
                                        <tr>
                                            <th width="60">Type</th>
                                            <th>Nama File</th>
                                            <th width="150">Diupload Oleh</th>
                                            <th width="120">Tanggal</th>
                                            <th width="100">Ukuran</th>
                                            <th width="150">Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($files as $file): ?>
                                            <?php
                                            $file_ext = strtolower(pathinfo($file['gambar'], PATHINFO_EXTENSION));
                                            $file_url = "../file_handler.php?id=" . $file['id'] . "&action=view";
                                            $download_url = "../file_handler.php?id=" . $file['id'] . "&action=download";

                                            // Determine icon and color based on file type
                                            $icon_class = 'fa-file file-type-default';
                                            if ($file_ext == 'pdf') $icon_class = 'fa-file-pdf file-type-pdf';
                                            elseif ($file_ext == 'dwg') $icon_class = 'fa-drafting-compass file-type-dwg';
                                            elseif (in_array($file_ext, ['jpg', 'jpeg', 'png', 'gif'])) $icon_class = 'fa-image file-type-jpg';
                                            ?>
                                            <tr class="file-item">
                                                <td class="text-center">
                                                    <i class="fas <?= $icon_class ?> file-icon"></i>
                                                </td>
                                                <td>
                                                    <div class="font-weight-bold"><?= htmlspecialchars($file['deskripsi']) ?></div>
                                                    <small class="text-muted"><?= strtoupper($file_ext) ?> File</small>
                                                </td>
                                                <td>
                                                    <span class="badge badge-secondary"><?= htmlspecialchars($file['uploader_name'] ?? 'Unknown') ?></span>
                                                </td>
                                                <td>
                                                    <small><?= date('d/m/Y', strtotime($file['created_at'])) ?></small><br>
                                                    <small class="text-muted"><?= date('H:i', strtotime($file['created_at'])) ?></small>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?= $file['file_size'] ? formatFileSize($file['file_size']) : 'Unknown' ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="<?= $file_url ?>" target="_blank" class="btn btn-info btn-sm btn-action" title="Lihat File">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?= $download_url ?>" class="btn btn-success btn-sm btn-action" title="Download File">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; Client Portal 2025</span>
                    </div>
                </div>
            </footer>
        </div>
    </div>



    <!-- Bootstrap core JavaScript-->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <script>
        // Add smooth animations and interactions
        $(document).ready(function() {
            // Add hover effects to file items
            $('.file-item').hover(
                function() {
                    $(this).addClass('shadow-sm');
                },
                function() {
                    $(this).removeClass('shadow-sm');
                }
            );

            // Add loading state to buttons
            $('.btn-action').click(function() {
                var btn = $(this);
                var originalHtml = btn.html();
                btn.html('<i class="fas fa-spinner fa-spin"></i>');

                setTimeout(function() {
                    btn.html(originalHtml);
                }, 1000);
            });
        });

        function formatFileSize(bytes) {
            if (!bytes) return 'Unknown';
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
    </script>
</body>
</html>
