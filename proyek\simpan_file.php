<?php
// Start session only if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
require '../koneksi.php';
require '../integration_handler.php';

// Check if user is logged in and has proper access
if (!isset($_SESSION['nama']) || $_SESSION['level'] != 'proyek') {
    http_response_code(403);
    die('Access denied');
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Sanitize input
    $deskripsi = mysqli_real_escape_string($koneksi, trim($_POST['deskripsi']));
    $uploaded_by = $_SESSION['id_petugas'] ?? 0;

    // Validate description
    if (empty($deskripsi) || strlen($deskripsi) > 200) {
        echo "<div class='alert alert-danger'>Deskripsi harus diisi dan maksimal 200 karakter.</div>";
        exit;
    }

    // File validation
    if (!isset($_FILES['gambar']) || $_FILES['gambar']['error'] !== UPLOAD_ERR_OK) {
        echo "<div class='alert alert-danger'>Error dalam upload file.</div>";
        exit;
    }

    $file = $_FILES['gambar'];

    // Security checks
    $maxFileSize = 50 * 1024 * 1024; // 50MB
    $allowedExt = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'obj', 'stl', 'dwg'];
    $allowedMimeTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
        'application/pdf', 'application/octet-stream',
        'application/acad', 'image/vnd.dwg'
    ];

    // Check file size
    if ($file['size'] > $maxFileSize) {
        echo "<div class='alert alert-danger'>Ukuran file terlalu besar. Maksimal 50MB.</div>";
        exit;
    }

    // Get file info
    $originalName = basename($file['name']);
    $fileExt = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
    $fileMimeType = mime_content_type($file['tmp_name']);

    // Validate file extension
    if (!in_array($fileExt, $allowedExt)) {
        echo "<div class='alert alert-danger'>Ekstensi file tidak diperbolehkan. Hanya: " . implode(', ', $allowedExt) . "</div>";
        exit;
    }

    // Validate MIME type
    if (!in_array($fileMimeType, $allowedMimeTypes)) {
        echo "<div class='alert alert-danger'>Jenis file tidak valid.</div>";
        exit;
    }

    // Additional security checks for images
    if (in_array($fileExt, ['jpg', 'jpeg', 'png', 'gif'])) {
        $imageInfo = getimagesize($file['tmp_name']);
        if ($imageInfo === false) {
            echo "<div class='alert alert-danger'>File gambar tidak valid.</div>";
            exit;
        }
    }

    // Generate secure filename
    $targetDir = "../file_proyek/";
    $fileHash = hash('sha256', $originalName . time() . $uploaded_by);
    $namaUnik = substr($fileHash, 0, 16) . "_" . preg_replace("/[^a-zA-Z0-9.\-_]/", "_", $originalName);
    $targetFile = $targetDir . $namaUnik;

    // Create directory if not exists with proper permissions
    if (!is_dir($targetDir)) {
        if (!mkdir($targetDir, 0755, true)) {
            echo "<div class='alert alert-danger'>Gagal membuat direktori upload.</div>";
            exit;
        }
    }

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $targetFile)) {
        // Set proper file permissions
        chmod($targetFile, 0644);

        // Insert to database using prepared statement
        $sql = "INSERT INTO file_gambar (deskripsi, gambar, uploaded_by, file_size, file_type, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())";
        $stmt = mysqli_prepare($koneksi, $sql);

        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "ssiss", $deskripsi, $namaUnik, $uploaded_by, $file['size'], $fileExt);

            if (mysqli_stmt_execute($stmt)) {
                $new_file_id = mysqli_insert_id($koneksi);

                // Create verification entry for the uploaded file
                createVerificationForFile($koneksi, $new_file_id);

                echo "<script>alert('File berhasil diupload!'); window.location.href='proyek.php?success=1';</script>";
            } else {
                // Delete uploaded file if database insert fails
                unlink($targetFile);
                echo "<div class='alert alert-danger'>Gagal menyimpan ke database: " . mysqli_error($koneksi) . "</div>";
            }

            mysqli_stmt_close($stmt);
        } else {
            // Delete uploaded file if prepare fails
            unlink($targetFile);
            echo "<div class='alert alert-danger'>Gagal menyiapkan query database.</div>";
        }
    } else {
        echo "<div class='alert alert-danger'>Gagal meng-upload file ke server.</div>";
    }
} else {
    echo "<div class='alert alert-danger'>Metode request tidak valid.</div>";
}
?>