<?php
session_start();
require '../koneksi.php';

// Check if user is logged in and has client access
if (!isset($_SESSION['id_petugas']) || $_SESSION['level'] != 'client') {
    header("Location: ../login.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $client_id = $_SESSION['id_petugas'];
    $title = trim($_POST['title']);
    $description = trim($_POST['description']);
    $revision_type = $_POST['revision_type'];
    $priority_level = $_POST['priority_level'];
    $tugas_id = !empty($_POST['tugas_id']) ? $_POST['tugas_id'] : null;
    $file_id = !empty($_POST['file_id']) ? $_POST['file_id'] : null;
    
    // Validate required fields
    if (empty($title) || empty($description) || empty($revision_type) || empty($priority_level)) {
        $_SESSION['error'] = "Semua field yang wajib diisi harus dilengkapi.";
        header("Location: ajukan_revisi.php");
        exit();
    }
    
    // Validate enum values
    $valid_types = ['design', 'document', 'specification', 'other'];
    $valid_priorities = ['low', 'medium', 'high', 'urgent'];
    
    if (!in_array($revision_type, $valid_types) || !in_array($priority_level, $valid_priorities)) {
        $_SESSION['error'] = "Data yang dikirim tidak valid.";
        header("Location: ajukan_revisi.php");
        exit();
    }
    
    // Start transaction
    mysqli_begin_transaction($koneksi);
    
    try {
        // Insert revision request
        $query = "INSERT INTO revision_requests (client_id, tugas_id, file_id, title, description, revision_type, priority_level, status, created_at) 
                  VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())";
        
        $stmt = mysqli_prepare($koneksi, $query);
        mysqli_stmt_bind_param($stmt, 'iisssss', $client_id, $tugas_id, $file_id, $title, $description, $revision_type, $priority_level);
        
        if (mysqli_stmt_execute($stmt)) {
            $revision_id = mysqli_insert_id($koneksi);
            
            // Commit transaction
            mysqli_commit($koneksi);
            
            $_SESSION['success'] = "Permintaan revisi berhasil dikirim! ID Permintaan: #" . $revision_id;
            header("Location: halaman_client.php");
            exit();
            
        } else {
            throw new Exception("Gagal menyimpan permintaan revisi.");
        }
        
    } catch (Exception $e) {
        // Rollback transaction
        mysqli_rollback($koneksi);
        
        $_SESSION['error'] = "Terjadi kesalahan: " . $e->getMessage();
        header("Location: ajukan_revisi.php");
        exit();
    }
    
} else {
    // If not POST request, redirect back
    header("Location: ajukan_revisi.php");
    exit();
}
?>
