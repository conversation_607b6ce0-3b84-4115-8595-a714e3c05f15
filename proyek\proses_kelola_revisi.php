<?php
session_start();
require '../koneksi.php';

// Check if user is logged in and has project team access
if (!isset($_SESSION['id_petugas']) || $_SESSION['level'] != 'proyek') {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit();
}

$action = $_POST['action'] ?? '';

switch ($action) {
    case 'get_revision_details':
        getRevisionDetails();
        break;
    case 'update_status':
        updateRevisionStatus();
        break;
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function getRevisionDetails() {
    global $koneksi;
    
    $revision_id = $_POST['revision_id'] ?? 0;
    
    $query = "SELECT rr.*, p.nama_petugas as client_name, tp.nama_tugas, fg.deskripsi as file_description, fg.gambar as file_name
              FROM revision_requests rr
              LEFT JOIN petugas p ON rr.client_id = p.id_petugas
              LEFT JOIN tugas_proyek tp ON rr.tugas_id = tp.id
              LEFT JOIN file_gambar fg ON rr.file_id = fg.id
              WHERE rr.id = ?";
    
    $stmt = mysqli_prepare($koneksi, $query);
    mysqli_stmt_bind_param($stmt, 'i', $revision_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    
    if ($row = mysqli_fetch_array($result)) {
        $priority_labels = [
            'urgent' => 'Mendesak',
            'high' => 'Tinggi',
            'medium' => 'Sedang',
            'low' => 'Rendah'
        ];
        
        $status_labels = [
            'pending' => 'Pending',
            'in_review' => 'Review',
            'in_progress' => 'Progress',
            'completed' => 'Selesai',
            'rejected' => 'Ditolak'
        ];
        
        $type_labels = [
            'design' => 'Desain',
            'document' => 'Dokumen',
            'specification' => 'Spesifikasi',
            'other' => 'Lainnya'
        ];
        
        $html = '<div class="row">
                    <div class="col-md-6">
                        <h6><strong>Informasi Umum</strong></h6>
                        <table class="table table-sm">
                            <tr><td><strong>Judul:</strong></td><td>' . htmlspecialchars($row['title']) . '</td></tr>
                            <tr><td><strong>Client:</strong></td><td>' . htmlspecialchars($row['client_name']) . '</td></tr>
                            <tr><td><strong>Jenis Revisi:</strong></td><td>' . ($type_labels[$row['revision_type']] ?? $row['revision_type']) . '</td></tr>
                            <tr><td><strong>Prioritas:</strong></td><td>
                                <span class="badge badge-' . 
                                    ($row['priority_level'] == 'urgent' ? 'danger' : 
                                     ($row['priority_level'] == 'high' ? 'warning' : 
                                      ($row['priority_level'] == 'medium' ? 'info' : 'secondary'))) . '">' . 
                                    ($priority_labels[$row['priority_level']] ?? $row['priority_level']) . '</span>
                            </td></tr>
                            <tr><td><strong>Status:</strong></td><td>
                                <span class="badge badge-' . 
                                    ($row['status'] == 'pending' ? 'warning' : 
                                     ($row['status'] == 'in_review' ? 'info' : 
                                      ($row['status'] == 'in_progress' ? 'primary' : 
                                       ($row['status'] == 'completed' ? 'success' : 'danger')))) . '">' . 
                                    ($status_labels[$row['status']] ?? $row['status']) . '</span>
                            </td></tr>
                            <tr><td><strong>Tanggal Dibuat:</strong></td><td>' . date('d/m/Y H:i', strtotime($row['created_at'])) . '</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6><strong>Detail Proyek</strong></h6>
                        <table class="table table-sm">
                            <tr><td><strong>Tugas Terkait:</strong></td><td>' . ($row['nama_tugas'] ? htmlspecialchars($row['nama_tugas']) : 'Tidak ada') . '</td></tr>
                            <tr><td><strong>File Terkait:</strong></td><td>' . ($row['file_description'] ? htmlspecialchars($row['file_description']) : 'Tidak ada') . '</td></tr>';
        
        if ($row['estimated_completion']) {
            $html .= '<tr><td><strong>Estimasi Selesai:</strong></td><td>' . date('d/m/Y', strtotime($row['estimated_completion'])) . '</td></tr>';
        }
        
        if ($row['actual_completion']) {
            $html .= '<tr><td><strong>Tanggal Selesai:</strong></td><td>' . date('d/m/Y H:i', strtotime($row['actual_completion'])) . '</td></tr>';
        }
        
        $html .= '      </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6><strong>Deskripsi Permintaan:</strong></h6>
                        <div class="border p-3 bg-light">
                            ' . nl2br(htmlspecialchars($row['description'])) . '
                        </div>
                    </div>
                </div>';
        
        if ($row['response_message']) {
            $html .= '<div class="row mt-3">
                        <div class="col-12">
                            <h6><strong>Respon Tim:</strong></h6>
                            <div class="border p-3 bg-info text-white">
                                ' . nl2br(htmlspecialchars($row['response_message'])) . '
                            </div>
                        </div>
                    </div>';
        }
        
        echo json_encode(['success' => true, 'html' => $html]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Revision request not found']);
    }
}

function updateRevisionStatus() {
    global $koneksi;
    
    $revision_id = $_POST['revision_id'] ?? 0;
    $new_status = $_POST['new_status'] ?? '';
    $response_message = $_POST['response_message'] ?? '';
    $estimated_completion = $_POST['estimated_completion'] ?? null;
    
    // Validate input
    if (!$revision_id || !$new_status) {
        echo json_encode(['success' => false, 'message' => 'Missing required fields']);
        return;
    }
    
    $valid_statuses = ['in_review', 'in_progress', 'completed', 'rejected'];
    if (!in_array($new_status, $valid_statuses)) {
        echo json_encode(['success' => false, 'message' => 'Invalid status']);
        return;
    }
    
    // Start transaction
    mysqli_begin_transaction($koneksi);
    
    try {
        // Prepare update query
        $update_fields = ['status = ?', 'response_message = ?', 'assigned_to = ?'];
        $params = [$new_status, $response_message, $_SESSION['id_petugas']];
        $types = 'ssi';
        
        // Add estimated completion if provided and status is in_progress
        if ($new_status === 'in_progress' && !empty($estimated_completion)) {
            $update_fields[] = 'estimated_completion = ?';
            $params[] = $estimated_completion;
            $types .= 's';
        }
        
        // Add actual completion if status is completed
        if ($new_status === 'completed') {
            $update_fields[] = 'actual_completion = NOW()';
        }
        
        $query = "UPDATE revision_requests SET " . implode(', ', $update_fields) . " WHERE id = ?";
        $params[] = $revision_id;
        $types .= 'i';
        
        $stmt = mysqli_prepare($koneksi, $query);
        mysqli_stmt_bind_param($stmt, $types, ...$params);
        
        if (mysqli_stmt_execute($stmt)) {
            mysqli_commit($koneksi);
            echo json_encode(['success' => true, 'message' => 'Status updated successfully']);
        } else {
            throw new Exception('Failed to update status');
        }
        
    } catch (Exception $e) {
        mysqli_rollback($koneksi);
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
