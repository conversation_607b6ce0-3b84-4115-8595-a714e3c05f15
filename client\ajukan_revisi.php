<?php
session_start();
require '../koneksi.php';

// Check if user is logged in and has client access
if (!isset($_SESSION['id_petugas']) || $_SESSION['level'] != 'client') {
    header("Location: ../login.php");
    exit();
}

// Get client's projects and files
$client_id = $_SESSION['id_petugas'];

$projects_query = "SELECT DISTINCT tp.id, tp.nama_kegiatan
                   FROM tugas_proyek tp
                   WHERE tp.status != 'batal'
                   ORDER BY tp.nama_kegiatan";
$projects_result = mysqli_query($koneksi, $projects_query);

$files_query = "SELECT fg.id, fg.deskripsi, fg.gambar, tp.nama_kegiatan
                FROM file_gambar fg
                LEFT JOIN tugas_proyek tp ON fg.tugas_id = tp.id
                ORDER BY fg.created_at DESC";
$files_result = mysqli_query($koneksi, $files_query);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Ajukan Revisi - Antosa Arsitek</title>
    <link href="../vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="../css/sb-admin-2.min.css" rel="stylesheet">
    <style>
        .priority-option {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .priority-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .priority-option.selected {
            border-color: #007bff;
            background-color: #f8f9fa;
        }
        .priority-badge {
            font-size: 0.9rem;
            padding: 0.5rem 1rem;
        }
    </style>
</head>

<body id="page-top">
    <div id="wrapper">
        <?php include 'client.php'; ?>
        
        <div id="content-wrapper" class="d-flex flex-column">
            <div id="content">
                <div class="container-fluid">
                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">
                            <i class="fas fa-edit mr-2"></i>Ajukan Permintaan Revisi
                        </h1>
                        <a href="halaman_client.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left mr-1"></i>Kembali ke Dashboard
                        </a>
                    </div>

                    <!-- Revision Request Form -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Form Permintaan Revisi</h6>
                                </div>
                                <div class="card-body">
                                    <form id="revisionForm" action="proses_revisi.php" method="POST">
                                        <div class="form-group">
                                            <label for="title">Judul Permintaan <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" required 
                                                   placeholder="Contoh: Revisi Desain Ruang Tamu">
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="tugas_id">Proyek Terkait</label>
                                                    <select class="form-control" id="tugas_id" name="tugas_id">
                                                        <option value="">Pilih Proyek (Opsional)</option>
                                                        <?php while ($project = mysqli_fetch_array($projects_result)): ?>
                                                            <option value="<?php echo $project['id']; ?>">
                                                                <?php echo htmlspecialchars($project['nama_kegiatan']); ?>
                                                            </option>
                                                        <?php endwhile; ?>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="file_id">File Terkait</label>
                                                    <select class="form-control" id="file_id" name="file_id">
                                                        <option value="">Pilih File (Opsional)</option>
                                                        <?php while ($file = mysqli_fetch_array($files_result)): ?>
                                                            <option value="<?php echo $file['id']; ?>">
                                                                <?php echo htmlspecialchars($file['deskripsi']); ?>
                                                                <?php if ($file['nama_kegiatan']): ?>
                                                                    - <?php echo htmlspecialchars($file['nama_kegiatan']); ?>
                                                                <?php endif; ?>
                                                            </option>
                                                        <?php endwhile; ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="revision_type">Jenis Revisi <span class="text-danger">*</span></label>
                                            <select class="form-control" id="revision_type" name="revision_type" required>
                                                <option value="">Pilih Jenis Revisi</option>
                                                <option value="design">Desain</option>
                                                <option value="document">Dokumen</option>
                                                <option value="specification">Spesifikasi</option>
                                                <option value="other">Lainnya</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Tingkat Prioritas <span class="text-danger">*</span></label>
                                            <div class="row mt-2">
                                                <div class="col-md-3 mb-2">
                                                    <div class="priority-option card text-center p-3" data-priority="low">
                                                        <span class="priority-badge badge badge-secondary">Rendah</span>
                                                        <small class="text-muted mt-2 d-block">Tidak mendesak</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <div class="priority-option card text-center p-3" data-priority="medium">
                                                        <span class="priority-badge badge badge-info">Sedang</span>
                                                        <small class="text-muted mt-2 d-block">Perlu perhatian</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <div class="priority-option card text-center p-3" data-priority="high">
                                                        <span class="priority-badge badge badge-warning">Tinggi</span>
                                                        <small class="text-muted mt-2 d-block">Segera ditangani</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 mb-2">
                                                    <div class="priority-option card text-center p-3" data-priority="urgent">
                                                        <span class="priority-badge badge badge-danger">Mendesak</span>
                                                        <small class="text-muted mt-2 d-block">Sangat penting</small>
                                                    </div>
                                                </div>
                                            </div>
                                            <input type="hidden" id="priority_level" name="priority_level" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="description">Deskripsi Detail <span class="text-danger">*</span></label>
                                            <textarea class="form-control" id="description" name="description" rows="6" required
                                                      placeholder="Jelaskan secara detail apa yang perlu direvisi, alasan revisi, dan hasil yang diharapkan..."></textarea>
                                            <small class="form-text text-muted">
                                                Semakin detail deskripsi yang Anda berikan, semakin mudah tim kami memahami kebutuhan revisi Anda.
                                            </small>
                                        </div>

                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary btn-lg">
                                                <i class="fas fa-paper-plane mr-2"></i>Kirim Permintaan Revisi
                                            </button>
                                            <a href="halaman_client.php" class="btn btn-secondary btn-lg ml-2">
                                                <i class="fas fa-times mr-2"></i>Batal
                                            </a>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Guidelines -->
                        <div class="col-lg-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-info">
                                        <i class="fas fa-info-circle mr-2"></i>Panduan Pengajuan Revisi
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <h6 class="text-primary">Tips untuk Permintaan Revisi yang Efektif:</h6>
                                    <ul class="list-unstyled">
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success mr-2"></i>
                                            <strong>Spesifik:</strong> Jelaskan dengan detail bagian mana yang perlu direvisi
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success mr-2"></i>
                                            <strong>Alasan:</strong> Berikan alasan mengapa revisi diperlukan
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success mr-2"></i>
                                            <strong>Ekspektasi:</strong> Jelaskan hasil yang diharapkan setelah revisi
                                        </li>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success mr-2"></i>
                                            <strong>Referensi:</strong> Sertakan referensi jika ada
                                        </li>
                                    </ul>
                                    
                                    <hr>
                                    
                                    <h6 class="text-warning">Tingkat Prioritas:</h6>
                                    <small class="text-muted">
                                        <strong>Rendah:</strong> Revisi kosmetik atau penyempurnaan<br>
                                        <strong>Sedang:</strong> Perubahan fungsional standar<br>
                                        <strong>Tinggi:</strong> Perubahan yang mempengaruhi timeline<br>
                                        <strong>Mendesak:</strong> Revisi kritis yang harus segera ditangani
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../vendor/jquery/jquery.min.js"></script>
    <script src="../vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="../js/sb-admin-2.min.js"></script>

    <script>
        $(document).ready(function() {
            // Priority selection
            $('.priority-option').click(function() {
                $('.priority-option').removeClass('selected');
                $(this).addClass('selected');
                $('#priority_level').val($(this).data('priority'));
            });

            // Form validation
            $('#revisionForm').submit(function(e) {
                if (!$('#priority_level').val()) {
                    e.preventDefault();
                    alert('Silakan pilih tingkat prioritas untuk permintaan revisi Anda.');
                    return false;
                }
            });
        });
    </script>

</body>
</html>
