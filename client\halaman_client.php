<?php
require '../koneksi.php';

// Hitung statistik untuk dashboard
$query_total = "SELECT COUNT(*) as total FROM tugas_proyek";
$result_total = mysqli_query($koneksi, $query_total);
$total_tugas = mysqli_fetch_array($result_total)['total'];

$query_selesai = "SELECT COUNT(*) as selesai FROM tugas_proyek WHERE status='selesai'";
$result_selesai = mysqli_query($koneksi, $query_selesai);
$tugas_selesai = mysqli_fetch_array($result_selesai)['selesai'];

$query_proses = "SELECT COUNT(*) as proses FROM tugas_proyek WHERE status='proses'";
$result_proses = mysqli_query($koneksi, $query_proses);
$tugas_proses = mysqli_fetch_array($result_proses)['proses'];

// Hitung persentase progress
$progress_percentage = $total_tugas > 0 ? round(($tugas_selesai / $total_tugas) * 100, 1) : 0;

// Ambil tugas terbaru (5 terakhir) untuk dashboard
$query_recent = "SELECT * FROM tugas_proyek ORDER BY tgl DESC LIMIT 5";
$result_recent = mysqli_query($koneksi, $query_recent);

// Ambil data file gambar terbaru dengan informasi uploader
$query_files = "SELECT fg.*, p.nama_petugas as uploader_name
                FROM file_gambar fg
                LEFT JOIN petugas p ON fg.uploaded_by = p.id_petugas
                ORDER BY fg.created_at DESC LIMIT 3";
$result_files = mysqli_query($koneksi, $query_files);

// Ambil statistik revision requests untuk client
$client_id = $_SESSION['id_petugas'];
$query_revisions = "SELECT
                        COUNT(*) as total_revisions,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_revisions,
                        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_revisions,
                        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_revisions
                    FROM revision_requests
                    WHERE client_id = $client_id";
$result_revisions = mysqli_query($koneksi, $query_revisions);
$revision_stats = mysqli_fetch_array($result_revisions);

// Ambil revision requests terbaru untuk client
$query_recent_revisions = "SELECT * FROM revision_requests
                           WHERE client_id = $client_id
                           ORDER BY created_at DESC LIMIT 3";
$result_recent_revisions = mysqli_query($koneksi, $query_recent_revisions);
?>

<!-- Page Heading -->
<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">Dashboard Client</h1>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Total Tugas Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Tugas</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $total_tugas; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tugas Selesai Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Tugas Selesai</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $tugas_selesai; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tugas Dalam Proses Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Tugas Dalam Proses
                        </div>
                        <div class="row no-gutters align-items-center">
                            <div class="col-auto">
                                <div class="h5 mb-0 mr-3 font-weight-bold text-gray-800"><?php echo $tugas_proses; ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-spinner fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Keseluruhan Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Progress Keseluruhan</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $progress_percentage; ?>%</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Revision Request Statistics -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Revisi</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $revision_stats['total_revisions']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-edit fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pending</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $revision_stats['pending_revisions']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Dalam Proses</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $revision_stats['in_progress_revisions']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cogs fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Selesai</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $revision_stats['completed_revisions']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Welcome Section -->
<div class="row">
    <div class="col-lg-12 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Selamat Datang, <?php echo $_SESSION['nama']; ?>!</h6>
            </div>
            <div class="card-body">
                <p class="mb-0">Selamat datang di portal client. Anda dapat melihat progress proyek dan informasi terkini mengenai perkembangan pekerjaan Anda di sini.</p>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="font-weight-bold">Progress Saat Ini:</h6>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $progress_percentage; ?>%"
                                 aria-valuenow="<?php echo $progress_percentage; ?>" aria-valuemin="0" aria-valuemax="100">
                                <?php echo $progress_percentage; ?>%
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="font-weight-bold">Status Proyek:</h6>
                        <p class="mb-0">
                            <?php
                            if ($progress_percentage == 100) {
                                echo '<span class="badge badge-success">Proyek Selesai</span>';
                            } elseif ($progress_percentage >= 75) {
                                echo '<span class="badge badge-info">Hampir Selesai</span>';
                            } elseif ($progress_percentage >= 50) {
                                echo '<span class="badge badge-warning">Dalam Progress</span>';
                            } elseif ($progress_percentage > 0) {
                                echo '<span class="badge badge-primary">Baru Dimulai</span>';
                            } else {
                                echo '<span class="badge badge-secondary">Belum Dimulai</span>';
                            }
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions Section -->
<div class="row mb-4">
    <div class="col-lg-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt mr-2"></i>Aksi Cepat
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="lihat_progress.php" class="btn btn-primary btn-block">
                            <i class="fas fa-chart-line mr-2"></i>Lihat Progress
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="ajukan_revisi.php" class="btn btn-warning btn-block">
                            <i class="fas fa-edit mr-2"></i>Ajukan Revisi
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="file_management.php" class="btn btn-info btn-block">
                            <i class="fas fa-folder mr-2"></i>File Management
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-secondary btn-block" onclick="alert('Fitur akan segera tersedia!')">
                            <i class="fas fa-comments mr-2"></i>Komunikasi
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">

    <!-- Recent Activities -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <!-- Card Header -->
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Aktivitas Terbaru</h6>
                <a href="lihat_progress.php" class="btn btn-primary btn-sm">Lihat Semua</a>
            </div>
            <!-- Card Body -->
            <div class="card-body">
                <?php if (mysqli_num_rows($result_recent) > 0): ?>
                    <div class="timeline">
                        <?php
                        while($row = mysqli_fetch_array($result_recent)) {
                            // Tentukan icon dan warna berdasarkan status
                            $icon_class = '';
                            $color_class = '';
                            switch($row['status']) {
                                case 'selesai':
                                    $icon_class = 'fa-check-circle';
                                    $color_class = 'text-success';
                                    break;
                                case 'proses':
                                    $icon_class = 'fa-spinner';
                                    $color_class = 'text-info';
                                    break;
                                case 'batal':
                                    $icon_class = 'fa-times-circle';
                                    $color_class = 'text-danger';
                                    break;
                                default:
                                    $icon_class = 'fa-clock';
                                    $color_class = 'text-secondary';
                            }
                        ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="mr-3">
                                <i class="fas <?php echo $icon_class; ?> <?php echo $color_class; ?> fa-lg"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo htmlspecialchars($row['nama_kegiatan']); ?></h6>
                                <p class="mb-1 text-muted small"><?php echo htmlspecialchars($row['deskripsi']); ?></p>
                                <small class="text-muted"><?php echo date('d M Y', strtotime($row['tgl'])); ?> -
                                    <span class="<?php echo $color_class; ?>"><?php echo ucfirst($row['status']); ?></span>
                                </small>
                            </div>
                        </div>
                        <?php } ?>
                    </div>
                <?php else: ?>
                    <p class="text-muted text-center">Belum ada aktivitas terbaru.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Revision Requests -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Permintaan Revisi Terbaru</h6>
            </div>
            <div class="card-body">
                <?php if (mysqli_num_rows($result_recent_revisions) > 0): ?>
                    <?php while($revision = mysqli_fetch_array($result_recent_revisions)): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="mr-3">
                            <i class="fas fa-edit text-<?php
                                switch($revision['status']) {
                                    case 'pending': echo 'warning'; break;
                                    case 'in_review': echo 'info'; break;
                                    case 'in_progress': echo 'primary'; break;
                                    case 'completed': echo 'success'; break;
                                    case 'rejected': echo 'danger'; break;
                                    default: echo 'secondary';
                                }
                            ?> fa-lg"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 small"><?php echo htmlspecialchars($revision['title']); ?></h6>
                            <small class="text-muted"><?php echo date('d M Y', strtotime($revision['created_at'])); ?></small>
                            <br>
                            <span class="badge badge-<?php
                                switch($revision['status']) {
                                    case 'pending': echo 'warning'; break;
                                    case 'in_review': echo 'info'; break;
                                    case 'in_progress': echo 'primary'; break;
                                    case 'completed': echo 'success'; break;
                                    case 'rejected': echo 'danger'; break;
                                    default: echo 'secondary';
                                }
                            ?> badge-sm">
                                <?php
                                $status_labels = [
                                    'pending' => 'Pending',
                                    'in_review' => 'Review',
                                    'in_progress' => 'Progress',
                                    'completed' => 'Selesai',
                                    'rejected' => 'Ditolak'
                                ];
                                echo $status_labels[$revision['status']] ?? $revision['status'];
                                ?>
                            </span>
                        </div>
                    </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="text-center text-muted">
                        <i class="fas fa-edit fa-2x mb-2"></i>
                        <p>Belum ada permintaan revisi</p>
                        <a href="ajukan_revisi.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus mr-1"></i>Ajukan Revisi
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- File Terbaru -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">File Desain Terbaru</h6>
            </div>
            <div class="card-body">
                <?php if (mysqli_num_rows($result_files) > 0): ?>
                    <?php while($file = mysqli_fetch_array($result_files)): ?>
                    <div class="d-flex align-items-center mb-3">
                        <div class="mr-3">
                            <?php
                            $file_ext = pathinfo($file['gambar'], PATHINFO_EXTENSION);
                            $icon_class = 'fa-file';
                            if (in_array(strtolower($file_ext), ['jpg', 'jpeg', 'png', 'gif'])) {
                                $icon_class = 'fa-image';
                            } elseif (strtolower($file_ext) == 'pdf') {
                                $icon_class = 'fa-file-pdf';
                            } elseif (strtolower($file_ext) == 'dwg') {
                                $icon_class = 'fa-drafting-compass';
                            }
                            ?>
                            <i class="fas <?php echo $icon_class; ?> text-primary fa-lg"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1 small"><?php echo htmlspecialchars($file['deskripsi']); ?></h6>
                            <small class="text-muted"><?php echo htmlspecialchars($file['gambar']); ?></small>
                            <br>
                            <div class="mt-1">
                                <a href="../file_handler.php?id=<?php echo $file['id']; ?>&action=view" target="_blank" class="btn btn-info btn-xs">
                                    <i class="fas fa-eye"></i> Lihat
                                </a>
                                <a href="../file_handler.php?id=<?php echo $file['id']; ?>&action=download" class="btn btn-success btn-xs">
                                    <i class="fas fa-download"></i> Download
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <p class="text-muted text-center small">Belum ada file desain.</p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Menu Cepat</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="lihat_progress.php" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-chart-line mr-2"></i>Lihat Progress Detail
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
