<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "proyek") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$priority_filter = isset($_GET['priority']) ? $_GET['priority'] : '';

// Build query with filters
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($status_filter)) {
    $where_conditions[] = "rr.status = ?";
    $params[] = $status_filter;
    $param_types .= 's';
}

if (!empty($priority_filter)) {
    $where_conditions[] = "rr.priority_level = ?";
    $params[] = $priority_filter;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get revision requests with client and project information
$query = "SELECT rr.*, 
                 p.nama_petugas as client_name,
                 tp.nama_kegiatan as project_name,
                 fg.gambar as file_name,
                 assigned.nama_petugas as assigned_name,
                 processed.nama_petugas as processed_name
          FROM revision_requests rr
          LEFT JOIN petugas p ON rr.client_id = p.id_petugas
          LEFT JOIN tugas_proyek tp ON rr.tugas_id = tp.id
          LEFT JOIN file_gambar fg ON rr.file_id = fg.id
          LEFT JOIN petugas assigned ON rr.assigned_to = assigned.id_petugas
          LEFT JOIN petugas processed ON rr.processed_by = processed.id_petugas
          $where_clause
          ORDER BY 
            CASE rr.priority_level 
                WHEN 'urgent' THEN 1 
                WHEN 'high' THEN 2 
                WHEN 'medium' THEN 3 
                WHEN 'low' THEN 4 
            END,
            rr.created_at DESC";

if (!empty($params)) {
    $stmt = mysqli_prepare($koneksi, $query);
    mysqli_stmt_bind_param($stmt, $param_types, ...$params);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
} else {
    $result = mysqli_query($koneksi, $query);
}

// Get statistics
$stats_query = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'in_review' THEN 1 ELSE 0 END) as in_review,
                    SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
                    SUM(CASE WHEN priority_level = 'urgent' THEN 1 ELSE 0 END) as urgent_count
                FROM revision_requests";
$stats_result = mysqli_query($koneksi, $stats_query);
$stats = mysqli_fetch_assoc($stats_result);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Kelola Revisi - Project Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <style>
        .priority-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .priority-low { background-color: #d1ecf1; color: #0c5460; }
        .priority-medium { background-color: #fff3cd; color: #856404; }
        .priority-high { background-color: #f8d7da; color: #721c24; }
        .priority-urgent { background-color: #dc3545; color: white; }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .status-pending { background-color: #ffeaa7; color: #2d3436; }
        .status-in_review { background-color: #74b9ff; color: white; }
        .status-in_progress { background-color: #fd79a8; color: white; }
        .status-completed { background-color: #00b894; color: white; }
        .status-rejected { background-color: #e17055; color: white; }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
            transition: all 0.2s ease-in-out;
        }
        
        .filter-section {
            background: #f8f9fc;
            border-radius: 0.35rem;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">
            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="proyek.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">antosa</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="proyek.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>
            </li>
          
            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">Interface</div>
            
            <li class="nav-item">
                <a class="nav-link" href="tugas_harian.php">
                    <i class="fas fa-fw fa-tasks"></i>
                    <span>Tugas Harian</span></a>
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="kelola_revisi.php">
                    <i class="fas fa-fw fa-edit"></i>
                    <span>Kelola Revisi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="uploud_file.php">
                    <i class="fas fa-fw fa-upload"></i>
                    <span>Upload File Desain</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="verifikasi.php">
                    <i class="fas fa-fw fa-check-double"></i>
                    <span>Verifikasi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="input_tugas.php">
                    <i class="fas fa-fw fa-plus-circle"></i>
                    <span>Input Tugas Harian</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>
            
            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>
        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <h1 class="h3 mb-0 text-gray-800">Kelola Revisi</h1>
                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Manajemen Permintaan Revisi</h1>
                        <a href="proyek.php" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Kembali ke Dashboard
                        </a>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-xl-2 col-md-4 mb-4">
                            <div class="card border-left-primary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['total']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-list fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 mb-4">
                            <div class="card border-left-warning shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Pending</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['pending']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 mb-4">
                            <div class="card border-left-info shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Review</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['in_review']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-search fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 mb-4">
                            <div class="card border-left-secondary shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">Progress</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['in_progress']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-cogs fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 mb-4">
                            <div class="card border-left-success shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Selesai</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['completed']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-check fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-xl-2 col-md-4 mb-4">
                            <div class="card border-left-danger shadow h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Mendesak</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $stats['urgent_count']; ?></div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Section -->
                    <div class="filter-section">
                        <form method="GET" action="" class="row align-items-end">
                            <div class="col-md-3">
                                <label for="status" class="form-label small font-weight-bold">Filter Status:</label>
                                <select name="status" id="status" class="form-control form-control-sm">
                                    <option value="">Semua Status</option>
                                    <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="in_review" <?php echo $status_filter == 'in_review' ? 'selected' : ''; ?>>In Review</option>
                                    <option value="in_progress" <?php echo $status_filter == 'in_progress' ? 'selected' : ''; ?>>In Progress</option>
                                    <option value="completed" <?php echo $status_filter == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="rejected" <?php echo $status_filter == 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="priority" class="form-label small font-weight-bold">Filter Prioritas:</label>
                                <select name="priority" id="priority" class="form-control form-control-sm">
                                    <option value="">Semua Prioritas</option>
                                    <option value="urgent" <?php echo $priority_filter == 'urgent' ? 'selected' : ''; ?>>Mendesak</option>
                                    <option value="high" <?php echo $priority_filter == 'high' ? 'selected' : ''; ?>>Tinggi</option>
                                    <option value="medium" <?php echo $priority_filter == 'medium' ? 'selected' : ''; ?>>Sedang</option>
                                    <option value="low" <?php echo $priority_filter == 'low' ? 'selected' : ''; ?>>Rendah</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-filter mr-1"></i>Filter
                                </button>
                                <a href="kelola_revisi.php" class="btn btn-secondary btn-sm ml-2">
                                    <i class="fas fa-times mr-1"></i>Reset
                                </a>
                            </div>
                        </form>
                    </div>

                    <!-- Revision Requests List -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-list mr-2"></i>Daftar Permintaan Revisi
                            </h6>
                        </div>
                        <div class="card-body">
                            <?php if (mysqli_num_rows($result) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Judul</th>
                                                <th>Client</th>
                                                <th>Jenis</th>
                                                <th>Prioritas</th>
                                                <th>Status</th>
                                                <th>Tanggal</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php while ($row = mysqli_fetch_assoc($result)): ?>
                                                <tr>
                                                    <td><?php echo $row['id']; ?></td>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($row['title']); ?></strong>
                                                        <?php if ($row['project_name']): ?>
                                                            <br><small class="text-muted">Proyek: <?php echo htmlspecialchars($row['project_name']); ?></small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($row['client_name']); ?></td>
                                                    <td>
                                                        <?php
                                                        $type_labels = [
                                                            'design' => 'Desain',
                                                            'document' => 'Dokumen',
                                                            'specification' => 'Spesifikasi',
                                                            'other' => 'Lainnya'
                                                        ];
                                                        echo $type_labels[$row['revision_type']] ?? $row['revision_type'];
                                                        ?>
                                                    </td>
                                                    <td>
                                                        <span class="priority-badge priority-<?php echo $row['priority_level']; ?>">
                                                            <?php
                                                            $priority_labels = [
                                                                'low' => 'Rendah',
                                                                'medium' => 'Sedang',
                                                                'high' => 'Tinggi',
                                                                'urgent' => 'Mendesak'
                                                            ];
                                                            echo $priority_labels[$row['priority_level']] ?? $row['priority_level'];
                                                            ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $row['status']; ?>">
                                                            <?php
                                                            $status_labels = [
                                                                'pending' => 'Pending',
                                                                'in_review' => 'Review',
                                                                'in_progress' => 'Progress',
                                                                'completed' => 'Selesai',
                                                                'rejected' => 'Ditolak'
                                                            ];
                                                            echo $status_labels[$row['status']] ?? $row['status'];
                                                            ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                                                    <td>
                                                        <button class="btn btn-info btn-sm" onclick="viewRevision(<?php echo $row['id']; ?>)">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <?php if ($row['status'] == 'pending'): ?>
                                                            <button class="btn btn-success btn-sm ml-1" onclick="processRevision(<?php echo $row['id']; ?>, 'in_review')">
                                                                <i class="fas fa-play"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-gray-300 mb-3"></i>
                                    <h5 class="text-gray-600">Tidak ada permintaan revisi</h5>
                                    <p class="text-muted">Belum ada permintaan revisi yang masuk dari client.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy;FOKUS UKK!!</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript -->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript -->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages -->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <script>
        function viewRevision(id) {
            // Open modal or redirect to detail page
            window.location.href = 'detail_revisi.php?id=' + id;
        }

        function processRevision(id, status) {
            if (confirm('Apakah Anda yakin ingin memproses permintaan revisi ini?')) {
                // Create form and submit
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = 'proses_kelola_revisi.php';

                var revisionIdInput = document.createElement('input');
                revisionIdInput.type = 'hidden';
                revisionIdInput.name = 'revision_id';
                revisionIdInput.value = id;

                var actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'update_status';

                var statusInput = document.createElement('input');
                statusInput.type = 'hidden';
                statusInput.name = 'status';
                statusInput.value = status;

                form.appendChild(revisionIdInput);
                form.appendChild(actionInput);
                form.appendChild(statusInput);

                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html>
