<?php
session_start();
require '../koneksi.php';

// Ambil data dari form
$nama = mysqli_real_escape_string($koneksi, $_POST['nama_kegiatan']);
$des = mysqli_real_escape_string($koneksi, $_POST['deskripsi']);
$tgl = $_POST['tgl'];

// Ambil ID petugas dari session (diasumsikan sudah login)
$created_by = isset($_SESSION['id_petugas']) ? $_SESSION['id_petugas'] : 1; // Default ke ID 1 jika tidak ada session
$assigned_to = $created_by; // Untuk sementara, assign ke diri sendiri

// Mulai transaction untuk memastikan konsistensi data
mysqli_begin_transaction($koneksi);

try {
    // Insert tugas baru dengan informasi tambahan
    $sql = "INSERT INTO tugas_proyek (nama_kegiatan, deskripsi, tgl, created_by, assigned_to, status, progress_percentage, tgl_mulai)
            VALUES ('$nama', '$des', '$tgl', '$created_by', '$assigned_to', 'proses', 25, CURDATE())";

    $result = mysqli_query($koneksi, $sql);

    if (!$result) {
        throw new Exception("Gagal menyimpan tugas: " . mysqli_error($koneksi));
    }

    // Ambil ID tugas yang baru dibuat
    $tugas_id = mysqli_insert_id($koneksi);

    // Trigger database akan otomatis membuat record verifikasi
    // Tapi kita bisa menambahkan log atau notifikasi di sini jika diperlukan

    // Commit transaction
    mysqli_commit($koneksi);

    echo "<script>
        alert('Data Berhasil Disimpan! Tugas telah dibuat dan siap untuk verifikasi.');
        window.location.href = 'proyek.php?success=1';
    </script>";

} catch (Exception $e) {
    // Rollback transaction jika ada error
    mysqli_rollback($koneksi);

    echo "<script>
        alert('Gagal menyimpan data: " . $e->getMessage() . "');
        window.location.href = 'proyek.php';
    </script>";
}
?>
