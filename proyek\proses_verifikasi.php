<?php
session_start();
require '../koneksi.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $verification_id = $_POST['verification_id'];
    $action = $_POST['action'];
    $verified_by = isset($_SESSION['id_petugas']) ? $_SESSION['id_petugas'] : 1;
    $verified_at = date('Y-m-d H:i:s');

    // Mulai transaction untuk memastikan konsistensi data
    mysqli_begin_transaction($koneksi);

    try {
        if ($action == 'verify' || $action == 'approve') {
            $catatan = mysqli_real_escape_string($koneksi, $_POST['catatan'] ?? '');

            // Update verifikasi dengan status approved
            $update_verification = mysqli_query($koneksi,
                "UPDATE verifikasi SET
                 catatan='$catatan',
                 status_verifikasi='approved',
                 verified_by='$verified_by',
                 verified_at='$verified_at'
                 WHERE id='$verification_id'"
            );

            if (!$update_verification) {
                throw new Exception('Gagal memproses verifikasi: ' . mysqli_error($koneksi));
            }

            // Trigger database akan otomatis update tugas_proyek
            $_SESSION['success_message'] = 'Verifikasi berhasil disetujui!';

        } elseif ($action == 'reject') {
            $catatan = mysqli_real_escape_string($koneksi, $_POST['catatan'] ?? '');
            $revision_notes = mysqli_real_escape_string($koneksi, $_POST['revision_notes'] ?? '');

            // Update verifikasi dengan status rejected
            $update_verification = mysqli_query($koneksi,
                "UPDATE verifikasi SET
                 catatan='$catatan',
                 status_verifikasi='rejected',
                 revision_notes='$revision_notes',
                 verified_by='$verified_by',
                 verified_at='$verified_at'
                 WHERE id='$verification_id'"
            );

            if (!$update_verification) {
                throw new Exception('Gagal menolak verifikasi: ' . mysqli_error($koneksi));
            }

            // Trigger database akan otomatis update tugas_proyek
            $_SESSION['success_message'] = 'Verifikasi ditolak. Tugas perlu diperbaiki.';

        } elseif ($action == 'revision') {
            $catatan = mysqli_real_escape_string($koneksi, $_POST['catatan'] ?? '');
            $revision_notes = mysqli_real_escape_string($koneksi, $_POST['revision_notes'] ?? '');

            // Update verifikasi dengan status revision
            $update_verification = mysqli_query($koneksi,
                "UPDATE verifikasi SET
                 catatan='$catatan',
                 status_verifikasi='revision',
                 revision_notes='$revision_notes',
                 verified_by='$verified_by',
                 verified_at='$verified_at'
                 WHERE id='$verification_id'"
            );

            if (!$update_verification) {
                throw new Exception('Gagal meminta revisi: ' . mysqli_error($koneksi));
            }

            // Trigger database akan otomatis update tugas_proyek
            $_SESSION['success_message'] = 'Revisi diminta. Tugas perlu diperbaiki.';

        } elseif ($action == 'reset') {
            // Reset verifikasi ke status pending
            $update_verification = mysqli_query($koneksi,
                "UPDATE verifikasi SET
                 catatan=NULL,
                 status_verifikasi='pending',
                 revision_notes=NULL,
                 verified_by=NULL,
                 verified_at=NULL
                 WHERE id='$verification_id'"
            );

            if (!$update_verification) {
                throw new Exception('Gagal mereset verifikasi: ' . mysqli_error($koneksi));
            }

            // Reset tugas proyek ke status proses
            $get_task = mysqli_query($koneksi, "SELECT tugas_id FROM verifikasi WHERE id='$verification_id'");
            $task_data = mysqli_fetch_array($get_task);
            $tugas_id = $task_data['tugas_id'];

            $update_task = mysqli_query($koneksi,
                "UPDATE tugas_proyek SET
                 status='proses',
                 progress_percentage=75,
                 tgl_selesai=NULL,
                 verified_by=NULL,
                 verified_at=NULL
                 WHERE id='$tugas_id'"
            );

            if (!$update_task) {
                throw new Exception('Gagal mereset tugas: ' . mysqli_error($koneksi));
            }

            $_SESSION['success_message'] = 'Verifikasi berhasil direset!';
        }

        // Commit transaction
        mysqli_commit($koneksi);

    } catch (Exception $e) {
        // Rollback transaction jika ada error
        mysqli_rollback($koneksi);
        $_SESSION['error_message'] = $e->getMessage();
    }

    header("Location: verifikasi.php");
    exit;
} else {
    header("Location: verifikasi.php");
    exit;
}
?>
