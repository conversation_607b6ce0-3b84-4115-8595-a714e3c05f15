<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

// Get available projects for selection
$query_projects = "SELECT * FROM tugas_proyek ORDER BY tgl DESC";
$result_projects = mysqli_query($koneksi, $query_projects);

// Get available files for reference
$query_files = "SELECT fg.*, tp.nama_kegiatan 
                FROM file_gambar fg 
                LEFT JOIN tugas_proyek tp ON fg.tugas_id = tp.id 
                ORDER BY fg.created_at DESC";
$result_files = mysqli_query($koneksi, $query_files);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Ajukan Revisi - Client Dashboard</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i" rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <style>
        .form-section {
            background: #f8f9fc;
            border-left: 4px solid #4e73df;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-radius: 0.35rem;
        }
        
        .priority-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 600;
            margin-right: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .priority-low { background-color: #d1ecf1; color: #0c5460; }
        .priority-medium { background-color: #fff3cd; color: #856404; }
        .priority-high { background-color: #f8d7da; color: #721c24; }
        .priority-urgent { background-color: #dc3545; color: white; }
        
        .priority-badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .priority-badge.selected {
            box-shadow: 0 0 0 2px #4e73df;
        }
        
        .file-preview {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
            border-radius: 0.25rem;
        }
    </style>
</head>

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper">
        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">
            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="client.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">antosa</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="client.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>         
            </li>
          
            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">Interface</div>
            
            <li class="nav-item">
                <a class="nav-link" href="lihat_progress.php">
                    <i class="fas fa-fw fa-chart-line"></i>
                    <span>Lihat Progress</span></a>
            </li>

            <li class="nav-item active">
                <a class="nav-link" href="ajukan_revisi.php">
                    <i class="fas fa-fw fa-edit"></i>
                    <span>Ajukan Revisi</span></a>
            </li>

            <li class="nav-item">
                <a class="nav-link" href="file_management.php">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>File Management</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

            <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>
            
            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>
        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">
                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <h1 class="h3 mb-0 text-gray-800">Ajukan Revisi</h1>
                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Form Pengajuan Revisi</h1>
                        <a href="client.php" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Kembali ke Dashboard
                        </a>
                    </div>

                    <!-- Revision Request Form -->
                    <div class="row">
                        <div class="col-lg-8">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-edit mr-2"></i>Formulir Pengajuan Revisi
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <form action="proses_revisi.php" method="post" enctype="multipart/form-data" id="revisionForm">
                                        <!-- Basic Information Section -->
                                        <div class="form-section">
                                            <h6 class="font-weight-bold text-primary mb-3">
                                                <i class="fas fa-info-circle mr-2"></i>Informasi Dasar
                                            </h6>
                                            
                                            <div class="form-group">
                                                <label for="title">Judul Revisi <span class="text-danger">*</span></label>
                                                <input type="text" class="form-control" id="title" name="title" 
                                                       placeholder="Contoh: Revisi Desain Lantai 2" required>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="revision_type">Jenis Revisi <span class="text-danger">*</span></label>
                                                        <select class="form-control" id="revision_type" name="revision_type" required>
                                                            <option value="">Pilih Jenis Revisi</option>
                                                            <option value="design">Revisi Desain</option>
                                                            <option value="document">Revisi Dokumen</option>
                                                            <option value="specification">Revisi Spesifikasi</option>
                                                            <option value="other">Lainnya</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Tingkat Prioritas <span class="text-danger">*</span></label>
                                                        <div class="mt-2">
                                                            <input type="hidden" id="priority_level" name="priority_level" required>
                                                            <span class="priority-badge priority-low" data-priority="low">Rendah</span>
                                                            <span class="priority-badge priority-medium" data-priority="medium">Sedang</span>
                                                            <span class="priority-badge priority-high" data-priority="high">Tinggi</span>
                                                            <span class="priority-badge priority-urgent" data-priority="urgent">Mendesak</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Project Reference Section -->
                                        <div class="form-section">
                                            <h6 class="font-weight-bold text-primary mb-3">
                                                <i class="fas fa-project-diagram mr-2"></i>Referensi Proyek
                                            </h6>
                                            
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="tugas_id">Proyek Terkait</label>
                                                        <select class="form-control" id="tugas_id" name="tugas_id">
                                                            <option value="">Pilih Proyek (Opsional)</option>
                                                            <?php while($project = mysqli_fetch_array($result_projects)): ?>
                                                                <option value="<?php echo $project['id']; ?>">
                                                                    <?php echo htmlspecialchars($project['nama_kegiatan']); ?> 
                                                                    (<?php echo date('d/m/Y', strtotime($project['tgl'])); ?>)
                                                                </option>
                                                            <?php endwhile; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="file_id">File Terkait</label>
                                                        <select class="form-control" id="file_id" name="file_id">
                                                            <option value="">Pilih File (Opsional)</option>
                                                            <?php while($file = mysqli_fetch_array($result_files)): ?>
                                                                <option value="<?php echo $file['id']; ?>">
                                                                    <?php echo htmlspecialchars($file['gambar']); ?>
                                                                    <?php if($file['nama_kegiatan']): ?>
                                                                        - <?php echo htmlspecialchars($file['nama_kegiatan']); ?>
                                                                    <?php endif; ?>
                                                                </option>
                                                            <?php endwhile; ?>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Description Section -->
                                        <div class="form-section">
                                            <h6 class="font-weight-bold text-primary mb-3">
                                                <i class="fas fa-align-left mr-2"></i>Deskripsi Revisi
                                            </h6>
                                            
                                            <div class="form-group">
                                                <label for="description">Deskripsi Detail <span class="text-danger">*</span></label>
                                                <textarea class="form-control" id="description" name="description" rows="5" 
                                                          placeholder="Jelaskan secara detail revisi yang diinginkan..." required></textarea>
                                                <small class="form-text text-muted">
                                                    Berikan penjelasan yang jelas dan detail tentang perubahan yang diinginkan.
                                                </small>
                                            </div>

                                            <div class="form-group">
                                                <label for="client_notes">Catatan Tambahan</label>
                                                <textarea class="form-control" id="client_notes" name="client_notes" rows="3" 
                                                          placeholder="Catatan atau informasi tambahan (opsional)"></textarea>
                                            </div>
                                        </div>

                                        <!-- Submit Section -->
                                        <div class="form-group text-right">
                                            <button type="button" class="btn btn-secondary mr-2" onclick="window.history.back()">
                                                <i class="fas fa-times mr-1"></i>Batal
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-paper-plane mr-1"></i>Ajukan Revisi
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar Information -->
                        <div class="col-lg-4">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">
                                        <i class="fas fa-info-circle mr-2"></i>Panduan Pengajuan
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <h6 class="font-weight-bold">Tips Pengajuan Revisi:</h6>
                                        <ul class="small text-muted">
                                            <li>Berikan judul yang jelas dan spesifik</li>
                                            <li>Pilih tingkat prioritas sesuai kebutuhan</li>
                                            <li>Jelaskan detail perubahan yang diinginkan</li>
                                            <li>Sertakan referensi proyek atau file jika ada</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <h6 class="font-weight-bold">Tingkat Prioritas:</h6>
                                        <div class="small">
                                            <div class="mb-1"><span class="priority-badge priority-low">Rendah</span> - Tidak mendesak</div>
                                            <div class="mb-1"><span class="priority-badge priority-medium">Sedang</span> - Perlu perhatian</div>
                                            <div class="mb-1"><span class="priority-badge priority-high">Tinggi</span> - Penting</div>
                                            <div class="mb-1"><span class="priority-badge priority-urgent">Mendesak</span> - Segera</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy;FOKUS UKK!!</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Bootstrap core JavaScript -->
    <script src="../tmp/vendor/jquery/jquery.min.js"></script>
    <script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript -->
    <script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages -->
    <script src="../tmp/js/sb-admin-2.min.js"></script>

    <script>
        $(document).ready(function() {
            // Priority selection
            $('.priority-badge').click(function() {
                $('.priority-badge').removeClass('selected');
                $(this).addClass('selected');
                $('#priority_level').val($(this).data('priority'));
            });

            // Form validation
            $('#revisionForm').submit(function(e) {
                if (!$('#priority_level').val()) {
                    e.preventDefault();
                    alert('Silakan pilih tingkat prioritas!');
                    return false;
                }
            });

            // Auto-resize textareas
            $('textarea').on('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });
        });
    </script>
</body>
</html>
