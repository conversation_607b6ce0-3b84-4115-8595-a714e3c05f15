<?php
session_start();

if (!isset($_SESSION['nama'])) {
    header("Location: ../index.php");
    exit;
}

if ($_SESSION['level'] != "client") {
    echo "<script>alert('Anda tidak memiliki akses ke halaman ini!'); window.location='../index.php';</script>";
    exit;
}

require '../koneksi.php';

// Enhanced query with verification status and file information
$query = "SELECT tp.*,
                 v.status_verifikasi,
                 v.verified_at as verification_date,
                 v.catatan as verification_notes,
                 p1.nama_petugas as created_by_name,
                 p2.nama_petugas as assigned_to_name,
                 p3.nama_petugas as verified_by_name,
                 COUNT(fg.id) as file_count,
                 GROUP_CONCAT(fg.gambar SEPARATOR ', ') as file_names
          FROM tugas_proyek tp
          LEFT JOIN verifikasi v ON tp.id = v.tugas_id
          LEFT JOIN petugas p1 ON tp.created_by = p1.id_petugas
          LEFT JOIN petugas p2 ON tp.assigned_to = p2.id_petugas
          LEFT JOIN petugas p3 ON tp.verified_by = p3.id_petugas
          LEFT JOIN file_gambar fg ON tp.id = fg.tugas_id
          GROUP BY tp.id
          ORDER BY tp.tgl DESC, tp.created_at DESC";
$result = mysqli_query($koneksi, $query);

// Enhanced statistics with verification status
$query_total = "SELECT COUNT(*) as total FROM tugas_proyek";
$result_total = mysqli_query($koneksi, $query_total);
$total_tugas = mysqli_fetch_array($result_total)['total'];

$query_selesai = "SELECT COUNT(*) as selesai FROM tugas_proyek WHERE status='selesai'";
$result_selesai = mysqli_query($koneksi, $query_selesai);
$tugas_selesai = mysqli_fetch_array($result_selesai)['selesai'];

$query_proses = "SELECT COUNT(*) as proses FROM tugas_proyek WHERE status IN ('proses', 'verifikasi')";
$result_proses = mysqli_query($koneksi, $query_proses);
$tugas_proses = mysqli_fetch_array($result_proses)['proses'];

$query_pending = "SELECT COUNT(*) as pending FROM tugas_proyek WHERE status='pending'";
$result_pending = mysqli_query($koneksi, $query_pending);
$tugas_pending = mysqli_fetch_array($result_pending)['pending'];

$query_batal = "SELECT COUNT(*) as batal FROM tugas_proyek WHERE status='batal'";
$result_batal = mysqli_query($koneksi, $query_batal);
$tugas_batal = mysqli_fetch_array($result_batal)['batal'];

// Enhanced progress calculation
$progress_percentage = $total_tugas > 0 ? round(($tugas_selesai / $total_tugas) * 100, 1) : 0;

// Get verification statistics
$query_verified = "SELECT COUNT(*) as verified FROM verifikasi WHERE status_verifikasi = 'approved'";
$result_verified = mysqli_query($koneksi, $query_verified);
$tugas_verified = mysqli_fetch_array($result_verified)['verified'];

$query_pending_verification = "SELECT COUNT(*) as pending_verification FROM verifikasi WHERE status_verifikasi = 'pending'";
$result_pending_verification = mysqli_query($koneksi, $query_pending_verification);
$tugas_pending_verification = mysqli_fetch_array($result_pending_verification)['pending_verification'];
?>
<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Lihat Progress</title>

    <!-- Custom fonts for this template-->
    <link href="../tmp/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="../tmp/css/sb-admin-2.min.css" rel="stylesheet">

    <!-- Enhanced CSS for Excel-like Table with Timeline Integration -->
    <style>
        :root {
            --primary-color: #4e73df;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --secondary-color: #858796;
            --light-color: #f8f9fc;
            --border-color: #d1d3e2;
        }

        .excel-container {
            background: #ffffff;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .view-toggle {
            background: var(--light-color);
            border-bottom: 1px solid var(--border-color);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .view-toggle-buttons {
            display: flex;
            gap: 5px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 3px;
        }

        .view-btn {
            background: transparent;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--secondary-color);
        }

        .view-btn.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 2px 4px rgba(78, 115, 223, 0.3);
        }

        .view-btn:hover:not(.active) {
            background: var(--light-color);
        }

        .excel-toolbar {
            background: var(--light-color);
            border-bottom: 1px solid var(--border-color);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .excel-toolbar-left {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .excel-toolbar-right {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .excel-search {
            position: relative;
        }

        .excel-search input {
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 8px 15px 8px 40px;
            font-size: 0.875rem;
            width: 250px;
            transition: all 0.3s ease;
        }

        .excel-search input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
            outline: none;
        }

        .excel-search i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--secondary-color);
        }

        .filter-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .excel-filter-btn {
            background: white;
            color: var(--secondary-color);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .excel-filter-btn:hover {
            background: var(--light-color);
            border-color: var(--primary-color);
        }

        .excel-filter-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            box-shadow: 0 2px 4px rgba(78, 115, 223, 0.3);
        }

        .excel-table-wrapper {
            overflow-x: auto;
            max-height: 75vh;
            border: 1px solid var(--border-color);
            border-radius: 0 0 8px 8px;
        }

        .excel-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 0.875rem;
            background: white;
            min-width: 1200px;
        }

        .excel-table thead {
            background: var(--light-color);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .excel-table th {
            border: 1px solid var(--border-color);
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            color: #5a5c69;
            background: var(--light-color);
            position: relative;
            cursor: pointer;
            user-select: none;
            white-space: nowrap;
            transition: all 0.3s ease;
        }

        .excel-table th:hover {
            background: #eaecf4;
        }

        .excel-table th.sortable::after {
            content: '⇅';
            position: absolute;
            right: 8px;
            color: var(--secondary-color);
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .excel-table th.sort-asc::after {
            content: '↑';
            color: var(--primary-color);
            opacity: 1;
        }

        .excel-table th.sort-desc::after {
            content: '↓';
            color: var(--primary-color);
            opacity: 1;
        }

        .excel-table td {
            border: 1px solid var(--border-color);
            padding: 12px;
            vertical-align: middle;
            background: white;
            transition: all 0.2s ease;
        }

        .excel-table tbody tr:hover {
            background: var(--light-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .excel-table tbody tr:nth-child(even) {
            background: #fafbfc;
        }

        .excel-table tbody tr:nth-child(even):hover {
            background: #f1f3f6;
        }

        .excel-table .row-number {
            background: var(--light-color);
            font-weight: 600;
            color: var(--secondary-color);
            text-align: center;
            width: 60px;
            border-right: 2px solid var(--border-color);
            position: sticky;
            left: 0;
            z-index: 5;
        }

        .status-cell {
            text-align: center;
            padding: 8px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 14px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 90px;
            justify-content: center;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .status-selesai {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border-color: #28a745;
        }

        .status-proses {
            background: linear-gradient(135deg, #cce7f0, #b8daff);
            color: #0c5460;
            border-color: #17a2b8;
        }

        .status-verifikasi {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            color: #856404;
            border-color: #ffc107;
        }

        .status-pending {
            background: linear-gradient(135deg, #e2e3e5, #d6d8db);
            color: #383d41;
            border-color: #6c757d;
        }

        .status-batal {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border-color: #dc3545;
        }

        .verification-badge {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            margin-top: 4px;
        }

        .verification-approved {
            background: #d1ecf1;
            color: #0c5460;
        }

        .verification-pending {
            background: #fff3cd;
            color: #856404;
        }

        .verification-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .progress-cell {
            min-width: 120px;
        }

        .progress-mini {
            width: 100%;
            height: 8px;
            background: #e3e6f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 4px;
        }

        .progress-mini-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--success-color), #28a745);
            transition: width 0.8s ease;
            border-radius: 4px;
        }

        .progress-text {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--secondary-color);
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 4px;
        }

        .file-count {
            background: var(--info-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .excel-stats {
            background: var(--light-color);
            border-top: 1px solid var(--border-color);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: #5a5c69;
            flex-wrap: wrap;
            gap: 15px;
        }

        .excel-stats-left {
            display: flex;
            gap: 25px;
            flex-wrap: wrap;
        }

        .excel-stats-right {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .stat-item i {
            font-size: 1rem;
        }

        .stat-value {
            font-weight: 700;
            font-size: 1.1rem;
        }

        .excel-actions {
            display: flex;
            gap: 8px;
        }

        .excel-btn {
            background: white;
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .excel-btn:hover {
            background: var(--light-color);
            border-color: var(--primary-color);
        }

        .excel-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* Timeline View Styles */
        .timeline-container {
            display: none;
            padding: 20px;
            background: white;
        }

        .timeline-container.active {
            display: block;
        }

        .timeline-item {
            display: flex;
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .timeline-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .timeline-date {
            min-width: 120px;
            padding-right: 20px;
            border-right: 2px solid var(--border-color);
        }

        .timeline-content {
            flex: 1;
            padding-left: 20px;
        }

        .timeline-title {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .timeline-description {
            color: var(--secondary-color);
            margin-bottom: 10px;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .excel-table {
                min-width: 1000px;
            }
        }

        @media (max-width: 768px) {
            .view-toggle {
                flex-direction: column;
                gap: 15px;
                align-items: stretch;
            }

            .excel-toolbar {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .excel-toolbar-left,
            .excel-toolbar-right {
                justify-content: center;
                flex-wrap: wrap;
            }

            .excel-search input {
                width: 100%;
                max-width: 300px;
            }

            .filter-group {
                justify-content: center;
            }

            .excel-stats {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .excel-stats-left,
            .excel-stats-right {
                justify-content: center;
                flex-wrap: wrap;
            }

            .timeline-item {
                flex-direction: column;
            }

            .timeline-date {
                min-width: auto;
                padding-right: 0;
                padding-bottom: 15px;
                border-right: none;
                border-bottom: 2px solid var(--border-color);
            }

            .timeline-content {
                padding-left: 0;
                padding-top: 15px;
            }
        }

        @media (max-width: 480px) {
            .excel-container {
                margin: 0 -15px;
                border-radius: 0;
            }

            .excel-table {
                min-width: 800px;
            }

            .view-toggle-buttons {
                width: 100%;
            }

            .view-btn {
                flex: 1;
            }
        }
    </style>

</head>
<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="client.php">
                <div class="sidebar-brand-icon rotate-n-15">
                    <i class="fas fa-keyboard"></i>
                </div>
                <div class="sidebar-brand-text mx-3">antosa</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item">
                <a class="nav-link" href="client.php">
                    <i class="fas fa-fw fa-tachometer-alt"></i>
                    <span>Dashboard</span></a>         
            </li>
          
            
            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <div class="sidebar-heading">
                Interface
            </div>
            
             <li class="nav-item active">
                <a class="nav-link" href="lihat_progress.php">
                    <i class="fas fa-fw fa-chart-line"></i>
                    <span>Lihat Progress</span></a>
            </li>

             <li class="nav-item">
                <a class="nav-link" href="file_management.php">
                    <i class="fas fa-fw fa-folder"></i>
                    <span>File Management</span></a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider d-none d-md-block">

             <li class="nav-item">
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Keluar</span></a>
            </li>
            
            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <button id="sidebarToggleTop" class="btn btn-link d-md-none rounded-circle mr-3">
                        <i class="fa fa-bars"></i>
                    </button>

                    <h1>Progress Proyek</h1>

                </nav>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Enhanced Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <div>
                            <h1 class="h3 mb-0 text-gray-800">
                                <i class="fas fa-chart-line mr-2"></i>Progress Proyek
                            </h1>
                            <p class="text-muted mb-0">Pantau perkembangan proyek secara real-time</p>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge badge-primary">
                                <i class="fas fa-database mr-1"></i><?php echo $total_tugas; ?> Total
                            </span>
                            <span class="badge badge-success">
                                <i class="fas fa-check mr-1"></i><?php echo $tugas_selesai; ?> Selesai
                            </span>
                            <span class="badge badge-info">
                                <i class="fas fa-clock mr-1"></i><?php echo $tugas_proses; ?> Proses
                            </span>
                        </div>
                    </div>

                    <!-- Enhanced Excel-like Table Container -->
                    <div class="excel-container">
                        <!-- View Toggle -->
                        <div class="view-toggle">
                            <div>
                                <h5 class="mb-0">
                                    <i class="fas fa-table mr-2"></i>Data Progress Proyek
                                </h5>
                                <small class="text-muted">Lihat progress dalam format tabel atau timeline</small>
                            </div>
                            <div class="view-toggle-buttons">
                                <button class="view-btn active" onclick="switchView('table')" id="view-table">
                                    <i class="fas fa-table mr-1"></i>Tabel
                                </button>
                                <button class="view-btn" onclick="switchView('timeline')" id="view-timeline">
                                    <i class="fas fa-stream mr-1"></i>Timeline
                                </button>
                            </div>
                        </div>

                        <!-- Enhanced Toolbar -->
                        <div class="excel-toolbar">
                            <div class="excel-toolbar-left">
                                <div class="excel-search">
                                    <i class="fas fa-search"></i>
                                    <input type="text" id="searchInput" placeholder="Cari nama kegiatan, deskripsi, atau tanggal..." onkeyup="searchTable()">
                                </div>
                                <div class="filter-group">
                                    <button class="excel-filter-btn active" onclick="filterStatus('all')" id="filter-all">
                                        <i class="fas fa-list"></i>Semua
                                    </button>
                                    <button class="excel-filter-btn" onclick="filterStatus('selesai')" id="filter-selesai">
                                        <i class="fas fa-check"></i>Selesai
                                    </button>
                                    <button class="excel-filter-btn" onclick="filterStatus('proses')" id="filter-proses">
                                        <i class="fas fa-cog"></i>Proses
                                    </button>
                                    <button class="excel-filter-btn" onclick="filterStatus('verifikasi')" id="filter-verifikasi">
                                        <i class="fas fa-eye"></i>Verifikasi
                                    </button>
                                    <button class="excel-filter-btn" onclick="filterStatus('pending')" id="filter-pending">
                                        <i class="fas fa-clock"></i>Pending
                                    </button>
                                    <button class="excel-filter-btn" onclick="filterStatus('batal')" id="filter-batal">
                                        <i class="fas fa-times"></i>Batal
                                    </button>
                                </div>
                            </div>
                            <div class="excel-toolbar-right">
                                <div class="excel-actions">
                                    <button class="excel-btn" onclick="exportData()">
                                        <i class="fas fa-download"></i>Export
                                    </button>
                                    <button class="excel-btn" onclick="refreshData()">
                                        <i class="fas fa-sync"></i>Refresh
                                    </button>
                                    <button class="excel-btn" onclick="toggleFullscreen()">
                                        <i class="fas fa-expand"></i>Fullscreen
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced Table View -->
                        <div class="excel-table-wrapper" id="table-view">
                            <table class="excel-table" id="progressTable">
                                <thead>
                                    <tr>
                                        <th class="row-number">#</th>
                                        <th class="sortable" onclick="sortTable(1)">
                                            <i class="fas fa-tasks mr-2"></i>Nama Kegiatan
                                        </th>
                                        <th class="sortable" onclick="sortTable(2)">
                                            <i class="fas fa-align-left mr-2"></i>Deskripsi
                                        </th>
                                        <th class="sortable" onclick="sortTable(3)">
                                            <i class="fas fa-calendar mr-2"></i>Tanggal
                                        </th>
                                        <th class="sortable" onclick="sortTable(4)">
                                            <i class="fas fa-flag mr-2"></i>Status
                                        </th>
                                        <th class="sortable" onclick="sortTable(5)">
                                            <i class="fas fa-shield-alt mr-2"></i>Verifikasi
                                        </th>
                                        <th class="sortable" onclick="sortTable(6)">
                                            <i class="fas fa-chart-pie mr-2"></i>Progress
                                        </th>
                                        <th class="sortable" onclick="sortTable(7)">
                                            <i class="fas fa-users mr-2"></i>Tim
                                        </th>
                                        <th>
                                            <i class="fas fa-folder mr-2"></i>File
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <?php
                                    // Reset result pointer
                                    mysqli_data_seek($result, 0);
                                    $no = 1;
                                    while($row = mysqli_fetch_array($result)) {
                                        // Enhanced status class determination
                                        $status_class = '';
                                        $status_icon = '';
                                        switch($row['status']) {
                                            case 'pending':
                                                $status_class = 'status-pending';
                                                $status_icon = 'fas fa-clock';
                                                break;
                                            case 'proses':
                                                $status_class = 'status-proses';
                                                $status_icon = 'fas fa-cog fa-spin';
                                                break;
                                            case 'verifikasi':
                                                $status_class = 'status-verifikasi';
                                                $status_icon = 'fas fa-eye';
                                                break;
                                            case 'selesai':
                                                $status_class = 'status-selesai';
                                                $status_icon = 'fas fa-check-circle';
                                                break;
                                            case 'batal':
                                                $status_class = 'status-batal';
                                                $status_icon = 'fas fa-times-circle';
                                                break;
                                        }

                                        // Verification status
                                        $verification_class = '';
                                        $verification_icon = '';
                                        $verification_text = '';
                                        if($row['status_verifikasi']) {
                                            switch($row['status_verifikasi']) {
                                                case 'approved':
                                                    $verification_class = 'verification-approved';
                                                    $verification_icon = 'fas fa-check';
                                                    $verification_text = 'Disetujui';
                                                    break;
                                                case 'rejected':
                                                    $verification_class = 'verification-rejected';
                                                    $verification_icon = 'fas fa-times';
                                                    $verification_text = 'Ditolak';
                                                    break;
                                                case 'revision':
                                                    $verification_class = 'verification-pending';
                                                    $verification_icon = 'fas fa-edit';
                                                    $verification_text = 'Revisi';
                                                    break;
                                                default:
                                                    $verification_class = 'verification-pending';
                                                    $verification_icon = 'fas fa-hourglass-half';
                                                    $verification_text = 'Menunggu';
                                            }
                                        } else {
                                            $verification_class = 'verification-pending';
                                            $verification_icon = 'fas fa-minus';
                                            $verification_text = 'Belum Ada';
                                        }

                                        // Format tanggal
                                        $tanggal_formatted = date('d/m/Y', strtotime($row['tgl']));
                                        $tanggal_sort = date('Y-m-d', strtotime($row['tgl']));
                                    ?>
                                    <tr data-status="<?php echo $row['status']; ?>" data-date="<?php echo $tanggal_sort; ?>" data-verification="<?php echo $row['status_verifikasi'] ?? 'none'; ?>">
                                        <td class="row-number"><?php echo $no++; ?></td>
                                        <td>
                                            <div>
                                                <strong><?php echo htmlspecialchars($row['nama_kegiatan']); ?></strong>
                                                <?php if($row['created_by_name']): ?>
                                                    <br><small class="text-muted">
                                                        <i class="fas fa-user mr-1"></i>Dibuat: <?php echo htmlspecialchars($row['created_by_name']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="text-muted"><?php echo htmlspecialchars($row['deskripsi']); ?></span>
                                        </td>
                                        <td data-sort="<?php echo $tanggal_sort; ?>">
                                            <div>
                                                <i class="fas fa-calendar-day mr-2 text-muted"></i>
                                                <strong><?php echo $tanggal_formatted; ?></strong>
                                                <?php if($row['tgl_mulai']): ?>
                                                    <br><small class="text-info">
                                                        <i class="fas fa-play mr-1"></i>Mulai: <?php echo date('d/m/Y', strtotime($row['tgl_mulai'])); ?>
                                                    </small>
                                                <?php endif; ?>
                                                <?php if($row['tgl_selesai']): ?>
                                                    <br><small class="text-success">
                                                        <i class="fas fa-flag-checkered mr-1"></i>Selesai: <?php echo date('d/m/Y', strtotime($row['tgl_selesai'])); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td class="status-cell">
                                            <span class="status-badge <?php echo $status_class; ?>">
                                                <i class="<?php echo $status_icon; ?>"></i>
                                                <?php
                                                $status_display = '';
                                                switch($row['status']) {
                                                    case 'pending': $status_display = 'Pending'; break;
                                                    case 'proses': $status_display = 'Proses'; break;
                                                    case 'verifikasi': $status_display = 'Verifikasi'; break;
                                                    case 'selesai': $status_display = 'Selesai'; break;
                                                    case 'batal': $status_display = 'Batal'; break;
                                                    default: $status_display = ucfirst($row['status']);
                                                }
                                                echo $status_display;
                                                ?>
                                            </span>
                                        </td>
                                        <td class="status-cell">
                                            <span class="verification-badge <?php echo $verification_class; ?>">
                                                <i class="<?php echo $verification_icon; ?>"></i>
                                                <?php echo $verification_text; ?>
                                            </span>
                                            <?php if($row['verification_date']): ?>
                                                <br><small class="text-muted">
                                                    <?php echo date('d/m/Y H:i', strtotime($row['verification_date'])); ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td class="progress-cell">
                                            <div class="progress-mini">
                                                <div class="progress-mini-bar" style="width: <?php echo $row['progress_percentage']; ?>%"></div>
                                            </div>
                                            <div class="progress-text"><?php echo $row['progress_percentage']; ?>%</div>
                                        </td>
                                        <td>
                                            <div>
                                                <?php if($row['assigned_to_name']): ?>
                                                    <small class="text-primary">
                                                        <i class="fas fa-user-cog mr-1"></i><?php echo htmlspecialchars($row['assigned_to_name']); ?>
                                                    </small>
                                                    <br>
                                                <?php endif; ?>
                                                <?php if($row['verified_by_name']): ?>
                                                    <small class="text-success">
                                                        <i class="fas fa-user-check mr-1"></i><?php echo htmlspecialchars($row['verified_by_name']); ?>
                                                    </small>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if($row['file_count'] > 0): ?>
                                                <div class="file-info">
                                                    <span class="file-count"><?php echo $row['file_count']; ?> file</span>
                                                    <a href="file_management.php?tugas_id=<?php echo $row['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </div>
                                                <?php if($row['file_names']): ?>
                                                    <small class="text-muted d-block mt-1">
                                                        <?php
                                                        $files = explode(', ', $row['file_names']);
                                                        echo htmlspecialchars(substr($files[0], 0, 20) . (strlen($files[0]) > 20 ? '...' : ''));
                                                        if(count($files) > 1) echo ' +' . (count($files)-1);
                                                        ?>
                                                    </small>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <small class="text-muted">
                                                    <i class="fas fa-minus"></i> Tidak ada file
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Timeline View -->
                        <div class="timeline-container" id="timeline-view">
                            <?php
                            // Reset result pointer for timeline
                            mysqli_data_seek($result, 0);
                            while($row = mysqli_fetch_array($result)) {
                                $status_color = '';
                                switch($row['status']) {
                                    case 'selesai': $status_color = 'success'; break;
                                    case 'proses': $status_color = 'info'; break;
                                    case 'verifikasi': $status_color = 'warning'; break;
                                    case 'pending': $status_color = 'secondary'; break;
                                    case 'batal': $status_color = 'danger'; break;
                                }
                            ?>
                            <div class="timeline-item" data-status="<?php echo $row['status']; ?>">
                                <div class="timeline-date">
                                    <div class="text-<?php echo $status_color; ?> font-weight-bold">
                                        <?php echo date('d', strtotime($row['tgl'])); ?>
                                    </div>
                                    <div class="text-muted small">
                                        <?php echo date('M Y', strtotime($row['tgl'])); ?>
                                    </div>
                                    <?php if($row['tgl_mulai'] && $row['tgl_selesai']): ?>
                                        <div class="text-muted small mt-2">
                                            <i class="fas fa-clock"></i>
                                            <?php
                                            $start = new DateTime($row['tgl_mulai']);
                                            $end = new DateTime($row['tgl_selesai']);
                                            $diff = $start->diff($end);
                                            echo $diff->days . ' hari';
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="timeline-content">
                                    <div class="timeline-title">
                                        <span class="badge badge-<?php echo $status_color; ?> mr-2">
                                            <?php echo ucfirst($row['status']); ?>
                                        </span>
                                        <?php echo htmlspecialchars($row['nama_kegiatan']); ?>
                                    </div>
                                    <div class="timeline-description">
                                        <?php echo htmlspecialchars($row['deskripsi']); ?>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="progress-mini" style="width: 150px;">
                                                <div class="progress-mini-bar" style="width: <?php echo $row['progress_percentage']; ?>%"></div>
                                            </div>
                                            <small class="text-muted"><?php echo $row['progress_percentage']; ?>% selesai</small>
                                        </div>
                                        <div class="text-right">
                                            <?php if($row['file_count'] > 0): ?>
                                                <span class="badge badge-info">
                                                    <i class="fas fa-file mr-1"></i><?php echo $row['file_count']; ?> file
                                                </span>
                                            <?php endif; ?>
                                            <?php if($row['status_verifikasi']): ?>
                                                <span class="badge badge-<?php echo $row['status_verifikasi'] == 'approved' ? 'success' : ($row['status_verifikasi'] == 'rejected' ? 'danger' : 'warning'); ?>">
                                                    <i class="fas fa-shield-alt mr-1"></i>
                                                    <?php
                                                    switch($row['status_verifikasi']) {
                                                        case 'approved': echo 'Disetujui'; break;
                                                        case 'rejected': echo 'Ditolak'; break;
                                                        case 'revision': echo 'Revisi'; break;
                                                        default: echo 'Menunggu';
                                                    }
                                                    ?>
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php } ?>
                        </div>

                        <!-- Enhanced Stats Footer -->
                        <div class="excel-stats">
                            <div class="excel-stats-left">
                                <div class="stat-item">
                                    <i class="fas fa-list text-primary"></i>
                                    <div>
                                        <div class="stat-value" id="totalRows"><?php echo $total_tugas; ?></div>
                                        <small>Total Tugas</small>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-check text-success"></i>
                                    <div>
                                        <div class="stat-value"><?php echo $tugas_selesai; ?></div>
                                        <small>Selesai</small>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-cog text-info"></i>
                                    <div>
                                        <div class="stat-value"><?php echo $tugas_proses; ?></div>
                                        <small>Dalam Proses</small>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-clock text-warning"></i>
                                    <div>
                                        <div class="stat-value"><?php echo $tugas_pending; ?></div>
                                        <small>Pending</small>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-shield-alt text-primary"></i>
                                    <div>
                                        <div class="stat-value"><?php echo $tugas_verified; ?></div>
                                        <small>Terverifikasi</small>
                                    </div>
                                </div>
                            </div>
                            <div class="excel-stats-right">
                                <div class="stat-item">
                                    <i class="fas fa-chart-pie text-success"></i>
                                    <div>
                                        <div class="stat-value"><?php echo $progress_percentage; ?>%</div>
                                        <small>Progress Keseluruhan</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy;FOKUS UKK!!</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

<!-- Bootstrap core JavaScript -->
<script src="../tmp/vendor/jquery/jquery.min.js"></script>
<script src="../tmp/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

<!-- Core plugin JavaScript -->
<script src="../tmp/vendor/jquery-easing/jquery.easing.min.js"></script>

<!-- Custom scripts for all pages -->
<script src="../tmp/js/sb-admin-2.min.js"></script>

<!-- Enhanced JavaScript for Excel-like Table with Timeline -->
<script>
let sortDirection = {};
let currentView = 'table';
let isFullscreen = false;

// View switching functionality
function switchView(view) {
    currentView = view;

    // Update button states
    document.querySelectorAll('.view-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById('view-' + view).classList.add('active');

    // Show/hide views
    const tableView = document.getElementById('table-view');
    const timelineView = document.getElementById('timeline-view');

    if (view === 'table') {
        tableView.style.display = 'block';
        timelineView.classList.remove('active');
    } else {
        tableView.style.display = 'none';
        timelineView.classList.add('active');
    }

    // Apply current filters to new view
    const activeFilter = document.querySelector('.excel-filter-btn.active');
    if (activeFilter) {
        const status = activeFilter.id.replace('filter-', '');
        if (view === 'timeline') {
            filterTimelineStatus(status);
        }
    }
}

// Enhanced search functionality
function searchTable() {
    const input = document.getElementById('searchInput');
    const filter = input.value.toLowerCase();
    let visibleCount = 0;

    if (currentView === 'table') {
        const table = document.getElementById('progressTable');
        const tbody = table.getElementsByTagName('tbody')[0];
        const rows = tbody.getElementsByTagName('tr');

        for (let i = 0; i < rows.length; i++) {
            const cells = rows[i].getElementsByTagName('td');
            let found = false;

            // Search in multiple columns
            for (let j = 1; j < cells.length; j++) {
                if (cells[j].textContent.toLowerCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }

            if (found) {
                rows[i].style.display = '';
                visibleCount++;
            } else {
                rows[i].style.display = 'none';
            }
        }
    } else {
        // Timeline search
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach(item => {
            const text = item.textContent.toLowerCase();
            if (text.indexOf(filter) > -1) {
                item.style.display = 'flex';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });
    }

    updateRowCount(visibleCount);
}

// Enhanced filter by status
function filterStatus(status) {
    // Update active button
    document.querySelectorAll('.excel-filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById('filter-' + status).classList.add('active');

    let visibleCount = 0;

    if (currentView === 'table') {
        const table = document.getElementById('progressTable');
        const tbody = table.getElementsByTagName('tbody')[0];
        const rows = tbody.getElementsByTagName('tr');

        for (let i = 0; i < rows.length; i++) {
            const rowStatus = rows[i].getAttribute('data-status');

            if (status === 'all' || rowStatus === status) {
                rows[i].style.display = '';
                visibleCount++;
            } else {
                rows[i].style.display = 'none';
            }
        }
    } else {
        filterTimelineStatus(status);
        return;
    }

    updateRowCount(visibleCount);
}

// Filter timeline by status
function filterTimelineStatus(status) {
    const timelineItems = document.querySelectorAll('.timeline-item');
    let visibleCount = 0;

    timelineItems.forEach(item => {
        const itemStatus = item.getAttribute('data-status');

        if (status === 'all' || itemStatus === status) {
            item.style.display = 'flex';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });

    updateRowCount(visibleCount);
}

// Sort table
function sortTable(columnIndex) {
    const table = document.getElementById('progressTable');
    const tbody = table.getElementsByTagName('tbody')[0];
    const rows = Array.from(tbody.getElementsByTagName('tr'));
    const header = table.getElementsByTagName('th')[columnIndex];

    // Determine sort direction
    const currentDirection = sortDirection[columnIndex] || 'asc';
    const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
    sortDirection[columnIndex] = newDirection;

    // Update header classes
    document.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    header.classList.add(newDirection === 'asc' ? 'sort-asc' : 'sort-desc');

    // Sort rows
    rows.sort((a, b) => {
        let aValue, bValue;

        if (columnIndex === 3) { // Date column
            aValue = a.getAttribute('data-date');
            bValue = b.getAttribute('data-date');
        } else {
            aValue = a.getElementsByTagName('td')[columnIndex].textContent.trim();
            bValue = b.getElementsByTagName('td')[columnIndex].textContent.trim();
        }

        if (newDirection === 'asc') {
            return aValue.localeCompare(bValue);
        } else {
            return bValue.localeCompare(aValue);
        }
    });

    // Re-append sorted rows
    rows.forEach(row => tbody.appendChild(row));

    // Update row numbers
    updateRowNumbers();
}

// Update row numbers
function updateRowNumbers() {
    const tbody = document.getElementById('tableBody');
    const rows = tbody.getElementsByTagName('tr');
    let visibleIndex = 1;

    for (let i = 0; i < rows.length; i++) {
        if (rows[i].style.display !== 'none') {
            rows[i].getElementsByClassName('row-number')[0].textContent = visibleIndex++;
        }
    }
}

// Update row count
function updateRowCount(count) {
    document.getElementById('totalRows').textContent = count;
    updateRowNumbers();
}

// Enhanced export functionality
function exportData() {
    const table = document.getElementById('progressTable');
    const rows = table.querySelectorAll('tbody tr:not([style*="display: none"])');

    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "No,Nama Kegiatan,Deskripsi,Tanggal,Status,Verifikasi,Progress,Tim,File\n";

    rows.forEach((row, index) => {
        const cells = row.querySelectorAll('td');
        const rowData = [
            index + 1,
            cells[1].textContent.trim().replace(/\n/g, ' '),
            cells[2].textContent.trim(),
            cells[3].textContent.trim().replace(/\n/g, ' '),
            cells[4].textContent.trim(),
            cells[5].textContent.trim().replace(/\n/g, ' '),
            cells[6].textContent.trim(),
            cells[7].textContent.trim().replace(/\n/g, ' '),
            cells[8].textContent.trim().replace(/\n/g, ' ')
        ];
        csvContent += rowData.map(field => `"${field}"`).join(',') + '\n';
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement('a');
    link.setAttribute('href', encodedUri);
    link.setAttribute('download', `progress_proyek_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Refresh data with loading indicator
function refreshData() {
    const refreshBtn = document.querySelector('[onclick="refreshData()"]');
    const originalText = refreshBtn.innerHTML;

    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>Refreshing...';
    refreshBtn.disabled = true;

    setTimeout(() => {
        location.reload();
    }, 500);
}

// Fullscreen toggle
function toggleFullscreen() {
    const container = document.querySelector('.excel-container');
    const btn = document.querySelector('[onclick="toggleFullscreen()"]');

    if (!isFullscreen) {
        container.style.position = 'fixed';
        container.style.top = '0';
        container.style.left = '0';
        container.style.width = '100vw';
        container.style.height = '100vh';
        container.style.zIndex = '9999';
        container.style.borderRadius = '0';

        btn.innerHTML = '<i class="fas fa-compress"></i>Exit Fullscreen';
        isFullscreen = true;
    } else {
        container.style.position = 'relative';
        container.style.top = 'auto';
        container.style.left = 'auto';
        container.style.width = 'auto';
        container.style.height = 'auto';
        container.style.zIndex = 'auto';
        container.style.borderRadius = '8px';

        btn.innerHTML = '<i class="fas fa-expand"></i>Fullscreen';
        isFullscreen = false;
    }
}

// Enhanced initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize view
    switchView('table');

    // Enhanced row hover effects
    const rows = document.querySelectorAll('#progressTable tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });

    // Animate progress bars with stagger effect
    const progressBars = document.querySelectorAll('.progress-mini-bar');
    progressBars.forEach((bar, index) => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, index * 100 + 500); // Delay start by 500ms
    });

    // Enhanced keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+F for search
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            document.getElementById('searchInput').focus();
        }

        // Escape to clear search
        if (e.key === 'Escape') {
            const searchInput = document.getElementById('searchInput');
            if (searchInput.value) {
                searchInput.value = '';
                searchTable();
            }
        }

        // Tab to switch views
        if (e.key === 'Tab' && e.ctrlKey) {
            e.preventDefault();
            const newView = currentView === 'table' ? 'timeline' : 'table';
            switchView(newView);
        }

        // F11 for fullscreen
        if (e.key === 'F11') {
            e.preventDefault();
            toggleFullscreen();
        }
    });

    // Auto-refresh every 5 minutes
    setInterval(() => {
        if (document.visibilityState === 'visible') {
            // Only refresh if page is visible
            const lastActivity = localStorage.getItem('lastActivity');
            const now = Date.now();
            if (!lastActivity || (now - parseInt(lastActivity)) > 300000) { // 5 minutes
                console.log('Auto-refreshing data...');
                location.reload();
            }
        }
    }, 300000); // 5 minutes

    // Track user activity
    ['click', 'keypress', 'scroll'].forEach(event => {
        document.addEventListener(event, () => {
            localStorage.setItem('lastActivity', Date.now().toString());
        });
    });

    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Add loading states to buttons
    document.querySelectorAll('.excel-btn, .excel-filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            if (!this.classList.contains('loading')) {
                this.classList.add('loading');
                setTimeout(() => {
                    this.classList.remove('loading');
                }, 1000);
            }
        });
    });
});

// Enhanced resize observer
if (window.ResizeObserver) {
    const resizeObserver = new ResizeObserver(entries => {
        entries.forEach(entry => {
            const width = entry.contentRect.width;
            const table = entry.target.querySelector('.excel-table');

            if (table) {
                // Adjust table layout based on container width
                if (width < 768) {
                    table.style.fontSize = '0.8rem';
                } else if (width < 1200) {
                    table.style.fontSize = '0.875rem';
                } else {
                    table.style.fontSize = '0.875rem';
                }
            }
        });
    });

    const tableWrapper = document.querySelector('.excel-table-wrapper');
    if (tableWrapper) {
        resizeObserver.observe(tableWrapper);
    }
}

// Add CSS for loading states
const style = document.createElement('style');
style.textContent = `
    .loading {
        opacity: 0.7;
        pointer-events: none;
    }
    .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin: -8px 0 0 -8px;
        border: 2px solid transparent;
        border-top: 2px solid currentColor;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>

</body>
</html>
